// API服务 - 专门处理与muyexi.cn的接口调用

// 全局变量存储任务信息
window.apiTaskInfo = {
    taskId: '',
    receiverName: 'abcdefg', // 可以根据需要修改
    queryValue: '',
    videoNumber: 1,
    grabNumber: 1,
    isTaskActive: false,
    isScriptRunning: false // 标记脚本是否正在运行
};

// 任务队列管理
window.taskQueue = {
    tasks: [], // 存储所有待执行的任务
    currentTaskIndex: 0, // 当前执行的任务索引
    isProcessing: false // 是否正在处理任务队列
};

// 定时器ID
let taskCheckTimer = null;

// API基础URL
const API_BASE_URL = 'https://start.muyexi.cn/tt-script';

// 1. 接受任务接口（单个任务）
function acceptTask(receiverName) {
    const currentTime = new Date().toLocaleTimeString();
    console.log(`📋 [${currentTime}] 正在接受任务，接收者: ${receiverName}`);
    console.log(`🔍 [${currentTime}] 当前脚本状态: isScriptRunning=${window.apiTaskInfo.isScriptRunning}, isTaskActive=${window.apiTaskInfo.isTaskActive}`);

    return $.get(`${API_BASE_URL}/task/${receiverName}`)
        .done(function (data) {
            console.log(`📋 [${currentTime}] 接受任务成功-----------------:`, data);

            // 检查返回结果是否表示有任务
            let hasTask = checkHasTask(data);

            if (hasTask) {
                console.log(`✅ [${currentTime}] 有新任务，准备执行脚本`);
                window.apiTaskInfo.isTaskActive = true;

                // 保存任务配置信息（如果是对象才有这些字段）
                if (typeof data === 'object' && data !== null) {
                    updateTaskInfoFromData(data, receiverName);
                } else {
                    // 如果只是返回true，使用默认值
                    setDefaultTaskInfo(receiverName);
                }

                logTaskConfig();
                return data;
            } else {
                console.log(`⏳ [${currentTime}] 暂无新任务`);
                window.apiTaskInfo.isTaskActive = false;
                return null;
            }
        })
        .fail(function (xhr, status, error) {
            console.log(`❌ [${currentTime}] 接受任务失败:`, error);
            window.apiTaskInfo.isTaskActive = false;
        });
}

// 批量接取所有任务
function acceptAllTasks(receiverName, maxTasks = 10) {
    const currentTime = new Date().toLocaleTimeString();
    console.log(`📋 [${currentTime}] 开始批量接取任务，接收者: ${receiverName}，最大任务数: ${maxTasks}`);

    const allTasks = [];
    let taskCount = 0;

    // 递归函数来接取任务
    function fetchNextTask() {
        if (taskCount >= maxTasks) {
            console.log(`📋 [${currentTime}] 已达到最大任务数限制: ${maxTasks}`);
            return Promise.resolve(allTasks);
        }

        return $.get(`${API_BASE_URL}/task/${receiverName}`)
            .then(function (data) {
                console.log(`📋 [${currentTime}] 第${taskCount + 1}次接取任务结果:`, data);

                let hasTask = checkHasTask(data);

                if (hasTask && typeof data === 'object' && data !== null) {
                    // 创建任务对象
                    const taskInfo = {
                        taskId: data.id || data.task_id || `task_${taskCount + 1}`,
                        receiverName: data.receiver_name || receiverName,
                        queryValue: data.query_value || '',
                        videoNumber: parseInt(data.video_number) || parseInt(data.videoNumber) || parseInt(data.video_count) || parseInt(data.videoCount) || parseInt(data.videos) || 1,
                        grabNumber: parseInt(data.grab_number) || parseInt(data.grabNumber) || parseInt(data.comment_number) || parseInt(data.commentNumber) || parseInt(data.comment_count) || parseInt(data.commentCount) || parseInt(data.comments) || parseInt(data.comment) || 1,
                        originalData: data
                    };

                    allTasks.push(taskInfo);
                    taskCount++;

                    console.log(`✅ [${currentTime}] 成功接取第${taskCount}个任务:`, {
                        任务ID: taskInfo.taskId,
                        搜索内容: taskInfo.queryValue,
                        视频数量: taskInfo.videoNumber,
                        评论数量: taskInfo.grabNumber
                    });

                    // 继续接取下一个任务
                    return fetchNextTask();
                } else {
                    console.log(`⏳ [${currentTime}] 没有更多任务，共接取到 ${allTasks.length} 个任务`);
                    return Promise.resolve(allTasks);
                }
            })
            .catch(function (error) {
                console.log(`❌ [${currentTime}] 接取第${taskCount + 1}个任务失败:`, error);
                return Promise.resolve(allTasks);
            });
    }

    return fetchNextTask().then(function (tasks) {
        console.log(`📋 [${currentTime}] 批量接取任务完成，共获得 ${tasks.length} 个任务`);
        return tasks;
    });
}

// 检查是否有任务
function checkHasTask(data) {
    if (data === true) {
        // 如果返回的就是true
        console.log('✅ 返回true，有新任务');
        return true;
    } else if (typeof data === 'object' && data !== null) {
        // 如果返回的是对象，检查是否包含任务信息
        if (data.status === true || data.hasTask === true || data.success === true) {
            console.log('✅ 返回对象包含任务状态字段');
            return true;
        } else if (data.id && data.query_value) {
            // 如果包含id和query_value字段，说明有任务
            console.log('✅ 返回对象包含任务数据（id和query_value）');
            return true;
        } else if (data.task_name || data.receiver_name) {
            // 如果包含任务名称或接收者名称，也说明有任务
            console.log('✅ 返回对象包含任务信息（task_name或receiver_name）');
            return true;
        }
    }
    return false;
}

// 从API数据更新任务信息
function updateTaskInfoFromData(data, receiverName) {
    console.log('📋 API返回的完整数据:', data);
    console.log('🔍 直接检查grab_number字段:', data.grab_number);
    console.log('🔍 直接检查video_number字段:', data.video_number);

    window.apiTaskInfo.taskId = data.id || data.task_id || '';
    window.apiTaskInfo.receiverName = data.receiver_name || receiverName;
    window.apiTaskInfo.queryValue = data.query_value || '';

    // 尝试多种可能的视频数量字段名
    window.apiTaskInfo.videoNumber = parseInt(data.video_number) ||
        parseInt(data.videoNumber) ||
        parseInt(data.video_count) ||
        parseInt(data.videoCount) ||
        parseInt(data.videos) || 1;

    // 尝试多种可能的评论数量字段名 - 确保grab_number被正确解析
    const grabNumberValue = data.grab_number ||
        data.grabNumber ||
        data.comment_number ||
        data.commentNumber ||
        data.comment_count ||
        data.commentCount ||
        data.comments ||
        data.comment || 1;
    window.apiTaskInfo.grabNumber = parseInt(grabNumberValue) || 1;

    console.log('🔧 评论数量解析过程:', {
        '原始grab_number': data.grab_number,
        '原始grab_number类型': typeof data.grab_number,
        'grabNumberValue': grabNumberValue,
        'parseInt结果': parseInt(grabNumberValue),
        '最终grabNumber': window.apiTaskInfo.grabNumber
    });

    console.log('📊 详细字段检查:', {
        'data.grab_number': data.grab_number,
        'data.grab_number类型': typeof data.grab_number,
        'data.video_number': data.video_number,
        'data.video_number类型': typeof data.video_number,
        '所有字段': Object.keys(data)
    });

    console.log('📊 解析后的字段值:', {
        视频数量原始值: data.video_number || data.videoNumber || data.video_count || data.videoCount || data.videos,
        评论数量原始值: data.grab_number || data.grabNumber || data.comment_number || data.commentNumber || data.comment_count || data.commentCount || data.comments || data.comment,
        最终视频数量: window.apiTaskInfo.videoNumber,
        最终评论数量: window.apiTaskInfo.grabNumber
    });
}

// 设置默认任务信息
function setDefaultTaskInfo(receiverName) {
    window.apiTaskInfo.taskId = 'default_task_id';
    window.apiTaskInfo.receiverName = receiverName;
    window.apiTaskInfo.queryValue = '默认搜索';
    window.apiTaskInfo.videoNumber = 1;
    window.apiTaskInfo.grabNumber = 1;
}

// 记录任务配置
function logTaskConfig() {
    console.log('📋 任务配置:', {
        任务ID: window.apiTaskInfo.taskId,
        搜索内容: window.apiTaskInfo.queryValue,
        视频数量: window.apiTaskInfo.videoNumber,
        评论数量: window.apiTaskInfo.grabNumber
    });
}

// 2. 开始任务接口
function startTask(taskId) {
    console.log(`🎬 正在开始任务，任务ID: ${taskId}`);
    return $.get(`${API_BASE_URL}/start/${taskId}`)
        .done(function (data) {
            console.log('🎬 开始任务成功-----------------:', data);
            return data;
        })
        .fail(function (xhr, status, error) {
            console.log('❌ 开始任务失败:', error);
            throw error;
        });
}

// 3. 发送数据接口
function sendUserData(userData) {
    console.log('📤 正在发送用户数据:', userData);

    return fetch(`${API_BASE_URL}/data`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
    })
        .then(response => response.json())
        .then(data => {
            console.log('📤 数据发送成功:', data);
            return data;
        })
        .catch(error => {
            console.log('❌ 数据发送失败:', error);
            throw error;
        });
}

// 4. 任务完成接口
function finishTask(taskId) {
    // 如果没有传入taskId，尝试从全局变量或localStorage获取
    if (!taskId) {
        if (window.apiTaskInfo && window.apiTaskInfo.taskId) {
            taskId = window.apiTaskInfo.taskId;
            console.log('🏁 使用全局保存的taskId:', taskId);
        } else {
            try {
                taskId = localStorage.getItem('currentTaskId') || '';
                if (taskId) {
                    console.log('🏁 使用localStorage备用taskId:', taskId);
                }
            } catch (error) {
                console.error('❌ 获取备用taskId失败:', error);
            }
        }
    }

    console.log(`🏁 正在标记任务完成，任务ID: ${taskId}`);

    if (!taskId) {
        console.error('❌ 无法获取taskId，任务完成标记失败');
        return Promise.reject(new Error('taskId为空'));
    }

    return $.get(`${API_BASE_URL}/task/finish/${taskId}`)
        .done(function (data) {
            console.log('🏁 任务完成标记成功-----------------:', data);
            return data;
        })
        .fail(function (xhr, status, error) {
            console.error('❌ 任务完成标记失败:', error);
            console.error('❌ 响应状态:', status);
            console.error('❌ 响应内容:', xhr.responseText);
            throw error;
        });
}

// 完整的API调用流程
function processUserDataWithAPI(completeUserData) {
    console.log('🚀 开始完整的API调用流程...');

    // 从localStorage获取搜索关键词
    const searchKeyword = localStorage.getItem('lastSearchKeyword') || '';

    // 构建API需要的数据格式
    const apiData = buildApiData(completeUserData, searchKeyword);

    // 步骤1: 接受任务
    acceptTask(window.apiTaskInfo.receiverName)
        .then(function (taskData) {
            if (taskData && taskData.id) {
                // 先更新全局任务信息
                window.apiTaskInfo.taskId = taskData.id;
                window.apiTaskInfo.receiverName = taskData.receiver_name || window.apiTaskInfo.receiverName;

                // 更新API数据中的任务信息
                apiData.task_id = taskData.id;
                apiData.receiver_name = taskData.receiver_name || window.apiTaskInfo.receiverName;
            }

            console.log('📋 任务ID设置:', {
                '接受任务返回的ID': taskData.id,
                '全局保存的ID': window.apiTaskInfo.taskId,
                '最终使用的task_id': apiData.task_id
            });

            // 步骤2: 开始任务
            return startTask(window.apiTaskInfo.taskId);
        })
        .then(function (startData) {
            // 步骤3: 发送用户数据
            return sendUserData(apiData);
        })
        .then(function (sendData) {
            console.log('✅ 完整API流程成功完成');
        })
        .catch(function (error) {
            console.log('❌ API流程失败:', error);
        });
}

// 构建API数据
function buildApiData(completeUserData, searchKeyword) {
    return {
        task_id: window.apiTaskInfo.taskId || '',
        query_value: searchKeyword,
        receiver_name: window.apiTaskInfo.receiverName || '',
        dy_comment_level: completeUserData.commentIndex || '',
        dy_comment: completeUserData.userComment || '',
        dy_nick_name: completeUserData.username || '',
        dy_user_id: completeUserData.douyinId || '',
        dy_remark: completeUserData.userBio || '',
        dy_ip: completeUserData.userLocation || '',
        dy_school: completeUserData.university || '',
        dy_attention_number: completeUserData.followingCount || '',
        dy_follower_number: completeUserData.followersCount || '',
        dy_production_number: completeUserData.likesCount || '',
        dy_head_img: completeUserData.avatarUrl || '',
        dy_age: completeUserData.age || '',
        dy_user_background: completeUserData.bgImageUrl || '',
        dy_like_tag: completeUserData.hasAuthorLike || 0,
        dy_compilations_name: completeUserData.compilationsName || '' // 添加编辑内容字段
    };
}

// 设置接收者名称
function setReceiverName(receiverName) {
    window.apiTaskInfo.receiverName = receiverName;
    console.log('📝 设置接收者名称:', receiverName);
}

// 获取当前任务信息
function getTaskInfo() {
    return window.apiTaskInfo;
}

// 重置脚本运行状态
function resetScriptRunningStatus() {
    window.apiTaskInfo.isScriptRunning = false;
    window.apiTaskInfo.isTaskActive = false;
    console.log('🔄 脚本运行状态已重置');
}

// 批量处理所有用户数据
function processBatchUserData(allUsersData) {
    console.log('🚀 开始批量处理用户数据，总数:', allUsersData.length);

    // 直接逐个发送每个用户的数据（任务已经在定时检查中处理）
    allUsersData.forEach((userData, index) => {
        setTimeout(() => {
            sendSingleUserData(userData, index + 1, allUsersData.length);
        }, index * 1000); // 每个请求间隔1秒
    });

    console.log('✅ 所有用户数据已加入发送队列');
}

// 发送单个用户数据（用于批量处理）
function sendSingleUserData(userData, currentIndex, totalCount) {
    console.log(`📤 发送第${currentIndex}/${totalCount}个用户数据`);

    const searchKeyword = localStorage.getItem('lastSearchKeyword') || '';
    const apiData = buildSingleUserApiData(userData, searchKeyword);

    console.log(`📤 第${currentIndex}个用户数据:`, apiData);
    console.log(`📋 第${currentIndex}个用户的task_id:`, apiData.task_id);

    sendUserData(apiData)
        .then(function (data) {
            console.log(`✅ 第${currentIndex}个用户数据发送成功`);
        })
        .catch(function (error) {
            console.log(`❌ 第${currentIndex}个用户数据发送失败:`, error);
        });
}

// 构建单个用户API数据
function buildSingleUserApiData(userData, searchKeyword) {
    return {
        task_id: window.apiTaskInfo.taskId || '', // 使用已获取的任务ID
        query_value: searchKeyword,
        receiver_name: window.apiTaskInfo.receiverName,
        dy_comment_level: userData.commentIndex || '',
        dy_comment: userData.userComment || '',
        dy_nick_name: userData.username || '',
        dy_user_id: userData.douyinId || '',
        dy_remark: userData.userBio || '',
        dy_ip: userData.userLocation || '',
        dy_school: userData.university || '',
        dy_attention_number: userData.followingCount || '',
        dy_follower_number: userData.followersCount || '',
        dy_production_number: userData.likesCount || '',
        dy_head_img: userData.avatarUrl || '',
    };
}

// 批量接取并处理任务队列
function startBatchTaskProcessor() {
    console.log('� 开始批量任务处理器');

    // 先停止之前的定时器
    stopTaskChecker();

    // 立即开始批量接取任务
    batchAcceptAndProcessTasks();

    // 设置定时器，每60秒检查一次（只有在没有任务队列时才会执行）
    taskCheckTimer = setInterval(() => {
        if (!window.taskQueue.isProcessing && window.taskQueue.tasks.length === 0) {
            console.log('🔍 任务队列为空，开始新一轮批量接取任务');
            batchAcceptAndProcessTasks();
        } else {
            console.log('⏸️ 任务队列正在处理中，跳过本次检查');
        }
    }, 60000);
}

// 批量接取并处理任务
function batchAcceptAndProcessTasks() {
    if (window.taskQueue.isProcessing) {
        console.log('⚠️ 任务队列正在处理中，跳过批量接取');
        return;
    }

    console.log('📋 开始批量接取所有可用任务...');

    acceptAllTasks(window.apiTaskInfo.receiverName)
        .then(function (tasks) {
            if (tasks && tasks.length > 0) {
                console.log(`✅ 成功接取到 ${tasks.length} 个任务，开始处理任务队列`);

                // 停止定时检查器
                stopTaskChecker();

                // 设置任务队列
                window.taskQueue.tasks = tasks;
                window.taskQueue.currentTaskIndex = 0;
                window.taskQueue.isProcessing = true;

                // 开始处理第一个任务
                processNextTaskInQueue();
            } else {
                console.log('⏳ 暂无可用任务，继续等待...');
            }
        })
        .catch(function (error) {
            console.log('❌ 批量接取任务失败:', error);
        });
}

// 处理队列中的下一个任务
function processNextTaskInQueue() {
    if (!window.taskQueue.isProcessing || window.taskQueue.currentTaskIndex >= window.taskQueue.tasks.length) {
        console.log('✅ 所有任务处理完成，重新开始任务检查');

        // 重置任务队列状态
        window.taskQueue.tasks = [];
        window.taskQueue.currentTaskIndex = 0;
        window.taskQueue.isProcessing = false;

        // 重新开始任务检查
        startBatchTaskProcessor();
        return;
    }

    const currentTask = window.taskQueue.tasks[window.taskQueue.currentTaskIndex];
    console.log(`🎬 开始处理第 ${window.taskQueue.currentTaskIndex + 1}/${window.taskQueue.tasks.length} 个任务:`, currentTask);

    // 设置当前任务信息到全局变量
    window.apiTaskInfo.taskId = currentTask.taskId;
    window.apiTaskInfo.receiverName = currentTask.receiverName;
    window.apiTaskInfo.queryValue = currentTask.queryValue;
    window.apiTaskInfo.videoNumber = currentTask.videoNumber;
    window.apiTaskInfo.grabNumber = currentTask.grabNumber;
    window.apiTaskInfo.isTaskActive = true;
    window.apiTaskInfo.isScriptRunning = true;

    console.log('📋 当前任务配置:', {
        任务ID: currentTask.taskId,
        搜索内容: currentTask.queryValue,
        视频数量: currentTask.videoNumber,
        评论数量: currentTask.grabNumber
    });

    // 开始任务
    startTask(currentTask.taskId)
        .then(function (startData) {
            console.log('✅ 任务开始成功，触发脚本执行');

            // 触发脚本执行
            triggerScriptExecution();
        })
        .catch(function (error) {
            console.log('❌ 任务开始失败:', error);

            // 跳过当前任务，处理下一个
            moveToNextTask();
        });
}

// 移动到下一个任务
function moveToNextTask() {
    console.log(`✅ 第 ${window.taskQueue.currentTaskIndex + 1} 个任务处理完成`);

    // 重置当前任务状态
    window.apiTaskInfo.isScriptRunning = false;
    window.apiTaskInfo.isTaskActive = false;

    // 移动到下一个任务
    window.taskQueue.currentTaskIndex++;

    // 延迟处理下一个任务，给系统一些缓冲时间
    setTimeout(() => {
        processNextTaskInQueue();
    }, 2000);
}

// 定时检查任务（每分钟一次）- 保持向后兼容
function startTaskChecker() {
    console.log('🕐 启动任务检查器（使用新的批量处理模式）');
    startBatchTaskProcessor();
}

// 检查新任务
function checkForNewTask() {
    const currentTime = new Date().toLocaleTimeString();

    // 如果脚本正在运行，跳过检查
    if (window.apiTaskInfo.isScriptRunning) {
        console.log(`⚠️ [${currentTime}] 脚本正在运行中，跳过任务检查`);
        return;
    }

    console.log(`🔍 [${currentTime}] 检查是否有新任务...`);

    acceptTask(window.apiTaskInfo.receiverName)
        .then(function (taskData) {
            if (taskData && window.apiTaskInfo.isTaskActive) {
                console.log(`🚀 [${currentTime}] 发现新任务，准备开始任务`);
                console.log(`📋 [${currentTime}] 任务数据:`, taskData);

                // 停止定时检查
                stopTaskChecker();

                // 开始任务
                console.log(`🎬 [${currentTime}] 调用开始任务接口，任务ID:`, window.apiTaskInfo.taskId);
                return startTask(window.apiTaskInfo.taskId);
            } else {
                console.log(`⏳ [${currentTime}] 暂无新任务，继续等待...`);
                return null;
            }
        })
        .then(function (startData) {
            if (startData) {
                console.log(`✅ [${currentTime}] 任务已开始成功，触发脚本执行`);
                console.log(`🎬 [${currentTime}] 开始任务响应数据:`, startData);

                // 触发脚本执行
                triggerScriptExecution();
            }
        })
        .catch(function (error) {
            console.log(`❌ [${currentTime}] 检查任务失败:`, error);
        });
}

// 停止任务检查
function stopTaskChecker() {
    if (taskCheckTimer) {
        clearInterval(taskCheckTimer);
        taskCheckTimer = null;
        console.log('⏹️ 已停止任务检查');
    }
}

// 触发脚本执行
function triggerScriptExecution() {
    // 检查脚本是否已经在运行
    if (window.apiTaskInfo.isScriptRunning) {
        console.log('⚠️ 脚本已在运行中，跳过本次执行');
        return;
    }

    console.log('🎬 开始执行抖音数据收集脚本');
    console.log('⏸️ 停止接受任务循环，直到任务结束...');

    // 设置脚本运行状态
    window.apiTaskInfo.isScriptRunning = true;

    // 停止任务检查器
    stopTaskChecker();

    const scriptConfig = {
        searchKeyword: window.apiTaskInfo.queryValue,
        videoCount: window.apiTaskInfo.videoNumber,
        commentCount: window.apiTaskInfo.grabNumber
    };

    console.log('📋 传递给脚本的配置:', scriptConfig);

    // 通知content-script开始执行
    if (window.startDouyinScript) {
        window.startDouyinScript(scriptConfig);

        // 注意：不需要手动重启任务检查
        // content-script.js中的finishSingleVideoCollection和finishAllVideosCollection
        // 会在任务完成后自动调用resetScriptRunningStatus和startTaskChecker
    } else {
        console.log('❌ 脚本执行函数不存在');
        // 如果脚本函数不存在，重置运行状态
        window.apiTaskInfo.isScriptRunning = false;
        // 脚本不存在时重新开始任务检查
        startTaskChecker();
    }
}

// 添加一个新函数，用于在任务完成后处理下一个任务
function restartTaskCheckerAfterCompletion() {
    console.log('✅ 当前任务已完成，准备处理下一个任务');

    // 如果是批量任务模式，处理下一个任务
    if (window.taskQueue.isProcessing && window.taskQueue.tasks.length > 0) {
        console.log('📋 批量任务模式：移动到下一个任务');
        moveToNextTask();
    } else {
        console.log('📋 单任务模式：重新开始任务检查');

        // 重置任务状态
        window.apiTaskInfo.isScriptRunning = false;
        window.apiTaskInfo.isTaskActive = false;

        // 清空任务ID，准备接受新任务
        window.apiTaskInfo.taskId = '';

        // 重新开始任务检查
        startTaskChecker();
    }
}

// 导出函数供其他文件使用
window.apiService = {
    acceptTask,
    acceptAllTasks,
    startTask,
    sendUserData,
    finishTask,
    processUserDataWithAPI,
    processBatchUserData,
    startTaskChecker,
    startBatchTaskProcessor,
    batchAcceptAndProcessTasks,
    processNextTaskInQueue,
    moveToNextTask,
    stopTaskChecker,
    checkForNewTask,
    setReceiverName,
    getTaskInfo,
    resetScriptRunningStatus,
    restartTaskCheckerAfterCompletion
};

console.log('📡 API服务已加载');

// 等待页面加载完成后开始任务检查
function initializeAfterPageLoad() {
    console.log('🚀 页面加载完成，开始批量任务处理器...');
    console.log('📋 当前接收者名称:', window.apiTaskInfo.receiverName);
    console.log('🔗 接受任务URL:', `${API_BASE_URL}/task/${window.apiTaskInfo.receiverName}`);
    console.log('🎯 使用批量任务处理模式：一次性接取所有任务，然后依次执行');
    startBatchTaskProcessor();
}

// 检查页面加载状态
if (document.readyState === 'loading') {
    // 页面还在加载中，等待加载完成
    document.addEventListener('DOMContentLoaded', initializeAfterPageLoad);
} else {
    // 页面已经加载完成
    initializeAfterPageLoad();
}


