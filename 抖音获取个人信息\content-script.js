(function () {
    //console.log('抖音群聊搜索插件已加载');

    // 初始化API服务
    initApiService();

    // 等待页面加载成功
    $(document).ready(function () {
        //console.log('页面加载成功');

        // 检查页面类型并执行相应操作
        checkPageTypeAndExecute();
    });

    // 初始化API服务
    function initApiService() {
        // 初始化全局任务信息,确保默认值
        if (!window.apiTaskInfo) {
            window.apiTaskInfo = {
                taskId: '', // 任务接取后从API响应中获取并全局保存
                receiverName: 'abcdefg',
                queryValue: '',
                videoNumber: 1,
                grabNumber: 1,
                isTaskActive: false,
                isScriptRunning: false
            };
            console.log('✅ 初始化全局任务信息完成，taskId将在任务接取后设置');
        }

        // 如果window.apiService已经存在,则跳过初始化
        if (window.apiService) {
            console.log('✅ API服务已初始化');
            return;
        }

        try {
            // 创建API服务对象
            window.apiService = {
                // API基础URL
                API_BASE_URL: 'https://start.muyexi.cn/tt-script',

                // 复制api-service.js中的方法
                finishTask: function (taskId) {
                    console.log(`🏁 正在标记任务完成，任务ID: ${taskId || window.apiTaskInfo.taskId}`);
                    // 使用传入的taskId,如果为空则使用全局任务ID
                    const useTaskId = taskId || window.apiTaskInfo.taskId;

                    if (!useTaskId) {
                        console.error('❌ 无法标记任务完成: 任务ID为空');
                        return Promise.reject(new Error('任务ID为空'));
                    }

                    const API_BASE_URL = 'https://start.muyexi.cn/tt-script';

                    return $.get(`${API_BASE_URL}/task/finish/${useTaskId}`)
                        .done(function (data) {
                            console.log('🏁 任务完成标记成功:', data);
                            return data;
                        })
                        .fail(function (xhr, status, error) {
                            console.error('❌ 任务完成标记失败:', error);
                            throw error;
                        });
                },

                // 其他API方法...
                resetScriptRunningStatus: function () {
                    if (window.apiTaskInfo) {
                        window.apiTaskInfo.isScriptRunning = false;
                        console.log('🔄 重置脚本运行状态');
                    }
                },

                startTaskChecker: function () {
                    console.log('🔄 启动任务检查器');
                    // 实际函数可能在api-service.js中定义
                }
            };

            console.log('✅ API服务初始化成功');
        } catch (error) {
            console.error('❌ API服务初始化失败:', error);
        }
    }

    // 检查页面类型并执行相应操作
    function checkPageTypeAndExecute() {
        // 检查是否在用户详情页面
        if (isUserDetailPage()) {
            //console.log('🔍 检测到用户详情页面，等待页面加载完成后获取用户信息');
            setTimeout(() => {
                getUserDetailInfo();
            }, 3000); // 恢复到3秒
        }
        // 检查是否在视频页面
        else if (isVideoPage()) {
            setTimeout(() => {
                addTestButtons();
            }, 2000); // 等待页面加载完成
        }
        // 检查是否在搜索页面
        else if (isSearchPage()) {
            //console.log('🔍 检测到搜索页面');
            // 搜索页面不需要特殊处理，等待用户操作
        }
        else {
            //console.log('🔍 未识别的页面类型，将定期检查页面变化');
            // 如果页面类型未识别，设置定期检查
            startPageMonitoring();
        }
    }

    // 开始页面监控
    function startPageMonitoring() {
        // 每2秒检查一次页面类型变化
        const monitorInterval = setInterval(() => {
            if (isUserDetailPage()) {
                //console.log('🔄 页面变化：检测到用户详情页面');
                clearInterval(monitorInterval);
                setTimeout(() => {
                    getUserDetailInfo();
                }, 1000);
            } else if (isVideoPage()) {
                //console.log('🔄 页面变化：检测到视频页面');
                clearInterval(monitorInterval);
                setTimeout(() => {
                    addTestButtons();
                }, 1000);
            }
        }, 2000);

        // 10秒后停止监控，避免无限循环
        setTimeout(() => {
            clearInterval(monitorInterval);
            //console.log('⏰ 页面监控已停止');
        }, 10000);
    }

    // 检查是否为用户详情页面
    function isUserDetailPage() {
        // 通过用户详情页面特有的DOM元素来判断
        const userDetailElement = $('#user_detail_element');

        // 使用更稳定的检测方式
        const hasUserDetail = userDetailElement.length > 0;
        const hasUserInfo = userDetailElement.find('[data-e2e="user-info"]').length > 0;
        const hasFollowInfo = userDetailElement.find('[data-e2e="user-info-follow"]').length > 0 ||
            userDetailElement.find('[data-e2e="user-info-fans"]').length > 0;

        console.log(`🔍 用户详情页面检测: 详情容器=${hasUserDetail}, 用户信息=${hasUserInfo}, 关注信息=${hasFollowInfo}`);

        // 只要有用户详情容器就认为是用户详情页面
        return hasUserDetail;
    }

    // 检查是否为视频页面
    function isVideoPage() {
        // 通过视频页面特有的DOM元素来判断
        const videoInfoWrap = $('#video-info-wrap');
        const commentContainer = $('#merge-all-comment-container');
        const videoElement = $('video');
        const accountName = $('#video-info-wrap > div.video-info-detail.isVideoInfoOptimise > div > div.account > div.account-name');

        const hasVideoInfo = videoInfoWrap.length > 0;
        const hasComments = commentContainer.length > 0;
        const hasVideo = videoElement.length > 0;
        const hasAccount = accountName.length > 0;

        //console.log(`🔍 视频页面检测: 视频信息=${hasVideoInfo}, 评论容器=${hasComments}, 视频元素=${hasVideo}, 账号信息=${hasAccount}`);

        return hasVideoInfo || hasComments || (hasVideo && hasAccount);
    }

    // 检查是否为搜索页面
    function isSearchPage() {
        // 通过搜索页面特有的DOM元素来判断
        const searchInput = $('input.st2xnJtZ.YIde9aUh[data-e2e="searchbar-input"]');
        const searchResults = $('.AMqhOzPC');
        const searchContainer = $('[data-e2e="search-result"]');

        const hasSearchInput = searchInput.length > 0;
        const hasSearchResults = searchResults.length > 0;
        const hasSearchContainer = searchContainer.length > 0;

        //console.log(`🔍 搜索页面检测: 搜索框=${hasSearchInput}, 搜索结果=${hasSearchResults}, 搜索容器=${hasSearchContainer}`);

        return hasSearchInput || hasSearchResults || hasSearchContainer;
    }

    // 添加测试按钮
    function addTestButtons() {
        // 检查是否已经添加过按钮
        if ($('#douyin-test-buttons').length > 0) {
            return;
        }

        //console.log('🔧 添加测试按钮...');

        // 创建按钮容器
        const buttonContainer = $('<div>', {
            id: 'douyin-test-buttons',
            css: {
                position: 'fixed',
                top: '10px',
                right: '10px',
                zIndex: '10000',
                backgroundColor: 'rgba(0,0,0,0.8)',
                padding: '15px',
                borderRadius: '10px',
                minWidth: '200px'
            }
        });

        // 创建标题
        const title = $('<div>', {
            text: '🧪 抖音测试工具',
            css: {
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                marginBottom: '10px',
                textAlign: 'center'
            }
        });

        // 创建状态显示
        const statusDiv = $('<div>', {
            id: 'douyin-test-status',
            text: '准备就绪',
            css: {
                color: '#00ff00',
                fontSize: '12px',
                marginTop: '10px',
                padding: '5px',
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '3px',
                textAlign: 'center'
            }
        });

        // 创建关闭按钮
        const closeButton = $('<button>', {
            text: '❌',
            css: {
                position: 'absolute',
                top: '5px',
                right: '5px',
                backgroundColor: 'transparent',
                color: 'white',
                border: 'none',
                cursor: 'pointer',
                fontSize: '16px'
            },
            click: function () {
                buttonContainer.remove();
            }
        });

        // 创建调试评论状态按钮
        const debugCommentsButton = $('<button>', {
            text: '🐛 调试评论状态',
            css: {
                backgroundColor: '#fd7e14',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px',
                marginBottom: '5px',
                display: 'block',
                width: '100%'
            },
            click: function () {
                debugCommentStatus();
            }
        });

        // 组装按钮容器
        buttonContainer.append(closeButton);
        buttonContainer.append(title);
        buttonContainer.append(debugCommentsButton);
        buttonContainer.append(statusDiv);

        // 添加到页面
        $('body').append(buttonContainer);

        //console.log('✅ 测试按钮添加完成');
    }

    // 测试滑动评论区功能
    function testScrollCommentArea() {
        //console.log('📜 测试滑动评论区功能...');
        updateTestStatus('📜 正在测试滑动评论区...');

        // 评论区容器选择器
        const commentContainerSelector = '#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV';
        const commentContainer = $(commentContainerSelector);

        if (commentContainer.length > 0) {
            //console.log('✅ 找到评论区容器，开始滑动测试');
            updateTestStatus('✅ 找到评论区容器，开始滑动');

            // 获取评论区元素
            const container = commentContainer[0];

            // 显示当前滚动位置
            //console.log('📊 当前滚动位置:', container.scrollTop);
            //console.log('📊 容器高度:', container.clientHeight);
            //console.log('📊 内容总高度:', container.scrollHeight);

            // 滑动到底部
            container.scrollTop = container.scrollHeight;

            //console.log('📜 已滑动评论区到底部');
            //console.log('📊 滑动后位置:', container.scrollTop);

            // 触发滚动事件
            const scrollEvent = new Event('scroll', { bubbles: true });
            container.dispatchEvent(scrollEvent);

            // 模拟鼠标滚轮事件
            const wheelEvent = new WheelEvent('wheel', {
                deltaY: 500,
                bubbles: true,
                cancelable: true
            });
            container.dispatchEvent(wheelEvent);

            // 检查是否有新评论加载
            setTimeout(() => {
                const newScrollHeight = container.scrollHeight;
                //console.log('📊 滑动后内容总高度:', newScrollHeight);

                // 统计当前可见的评论数量
                const comments = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div');
                //console.log('📊 当前评论数量:', comments.length);

                updateTestStatus(`✅ 滑动完成，当前有${comments.length}条评论`);

                // 测试查找第5条评论
                const fifthComment = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(5)');
                if (fifthComment.length > 0) {
                    //console.log('✅ 找到第5条评论');
                    updateTestStatus('✅ 找到第5条评论');
                } else {
                    //console.log('❌ 未找到第5条评论');
                    updateTestStatus('❌ 未找到第5条评论，可能需要更多滑动');
                }

            }, 2000);

        } else {
            //console.log('❌ 未找到评论区容器');
            updateTestStatus('❌ 未找到评论区容器');
        }
    }

    // 测试检测评论结束标识功能
    function testEndMarkerDetection() {
        console.log('🏁 测试检测评论结束标识功能...');
        updateTestStatus('🏁 正在检测评论结束标识...');

        // 检查是否出现了评论结束标识元素
        const endMarkerSelector = '#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div.fanRMYie';
        const endMarkerElement = $(endMarkerSelector);

        if (endMarkerElement.length > 0) {
            console.log('✅ 检测到评论结束标识元素(div.fanRMYie)');
            updateTestStatus('✅ 检测到评论结束标识元素');

            // 显示元素的详细信息
            const elementText = endMarkerElement.text().trim();
            const elementHtml = endMarkerElement.html();
            console.log('📄 结束标识元素内容:', elementText);
            console.log('📄 结束标识元素HTML:', elementHtml);

            updateTestStatus(`✅ 检测到结束标识: "${elementText}"`);
        } else {
            console.log('❌ 未检测到评论结束标识元素');
            updateTestStatus('❌ 未检测到评论结束标识元素');

            // 显示当前评论区的结构信息
            const commentContainer = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV');
            if (commentContainer.length > 0) {
                const children = commentContainer.children();
                console.log(`📊 评论区共有 ${children.length} 个子元素`);

                // 检查最后几个元素的类名
                children.slice(-3).each(function (index, element) {
                    const className = $(element).attr('class') || '无类名';
                    const text = $(element).text().trim().substring(0, 50) || '无文本';
                    console.log(`📄 倒数第${3 - index}个元素: 类名="${className}", 文本="${text}"`);
                });

                updateTestStatus(`📊 评论区有 ${children.length} 个子元素，未发现结束标识`);
            } else {
                updateTestStatus('❌ 未找到评论区容器');
            }
        }
    }

    // 测试批量收集功能
    function testBatchCollection() {
        //console.log('🚀 测试批量收集功能...');
        updateTestStatus('🚀 开始测试批量收集...');

        // 设置测试参数
        const testOptions = {
            comment: 2,  // 修改为统一使用comment字段
            includeSecondaryComments: false
        };

        // 保存测试选项
        try {
            localStorage.setItem('douyinSearchOptions', JSON.stringify(testOptions));
            //console.log('💾 测试选项已保存:', testOptions);
        } catch (error) {
            //console.log('保存测试选项失败:', error);
        }

        // 开始批量收集
        startBatchUserInfoCollection();
        updateTestStatus('✅ 批量收集已启动');
    }

    // 更新测试状态显示
    function updateTestStatus(message) {
        const statusElement = $('#douyin-test-status');
        if (statusElement.length > 0) {
            statusElement.text(message);
        }
        //console.log('📊 状态更新:', message);
    }

    // 测试空值处理功能
    function testNullHandling() {
        console.log('🔧 测试空值处理功能...');
        updateTestStatus('🔧 正在测试空值处理...');

        try {
            // 模拟userInfo为null的情况
            const userInfo = null;
            const avatarSrc = 'https://example.com/avatar.jpg';
            const commentText = '测试评论内容';
            const commentIndex = 1;
            const searchKeyword = '测试关键词';

            // 测试安全的对象展开和属性访问
            const completeUserData = {
                ...(userInfo || {}), // 安全展开用户详情信息，如果userInfo为null则使用空对象
                userComment: commentText, // 添加评论内容
                commentIndex: commentIndex, // 添加评论索引
                commentTimestamp: new Date().toLocaleString(), // 添加评论获取时间
                avatarUrl: (userInfo && userInfo.avatarUrl) || avatarSrc, // 安全访问avatarUrl属性
                searchKeyword: searchKeyword // 添加搜索关键词
            };

            console.log('✅ 空值处理测试成功');
            console.log('📄 测试结果:', completeUserData);
            updateTestStatus('✅ 空值处理测试成功，未出现错误');

            // 测试userInfo有值的情况
            const userInfoWithData = {
                username: '测试用户',
                douyinId: 'test123',
                avatarUrl: 'https://example.com/user-avatar.jpg'
            };

            const completeUserDataWithInfo = {
                ...(userInfoWithData || {}),
                userComment: commentText,
                commentIndex: commentIndex,
                commentTimestamp: new Date().toLocaleString(),
                avatarUrl: (userInfoWithData && userInfoWithData.avatarUrl) || avatarSrc,
                searchKeyword: searchKeyword
            };

            console.log('✅ 有数据情况测试成功');
            console.log('📄 有数据测试结果:', completeUserDataWithInfo);
            updateTestStatus('✅ 所有空值处理测试通过');

        } catch (error) {
            console.error('❌ 空值处理测试失败:', error);
            updateTestStatus(`❌ 空值处理测试失败: ${error.message}`);
        }
    }

    // 调试评论状态功能
    function debugCommentStatus() {
        console.log('🐛 开始调试评论状态...');
        updateTestStatus('🐛 正在调试评论状态...');

        try {
            // 检查评论区容器
            const commentContainer = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV');
            console.log('📊 评论区容器状态:', {
                found: commentContainer.length > 0,
                visible: commentContainer.is(':visible'),
                height: commentContainer.height(),
                scrollTop: commentContainer.length > 0 ? commentContainer[0].scrollTop : 'N/A',
                scrollHeight: commentContainer.length > 0 ? commentContainer[0].scrollHeight : 'N/A'
            });

            // 检查所有评论元素
            const allComments = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div');
            console.log(`📊 总评论元素数量: ${allComments.length}`);

            // 检查前10个评论的状态
            for (let i = 1; i <= Math.min(10, allComments.length); i++) {
                const commentSelector = `#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(${i})`;
                const avatarSelector = `${commentSelector} > div > div.VPtAXFCJ.comment-item-avatar > div > a > span > img`;

                const commentElement = $(commentSelector);
                const avatarElement = $(avatarSelector);

                console.log(`📊 第${i}条评论状态:`, {
                    commentExists: commentElement.length > 0,
                    avatarExists: avatarElement.length > 0,
                    commentVisible: commentElement.is(':visible'),
                    avatarVisible: avatarElement.is(':visible'),
                    commentClass: commentElement.attr('class'),
                    avatarSrc: avatarElement.attr('src') || 'N/A'
                });
            }

            // 检查结束标识
            const endMarkerElement = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div.fanRMYie');
            console.log('📊 结束标识状态:', {
                found: endMarkerElement.length > 0,
                visible: endMarkerElement.is(':visible'),
                text: endMarkerElement.text().trim()
            });

            // 检查多视频状态
            console.log('📊 多视频状态:', window.multiVideoState);
            console.log('📊 当前评论设置:', {
                totalComments: window.totalComments,
                currentCommentIndex: window.currentCommentIndex,
                allUserDataLength: window.allUserData ? window.allUserData.length : 'N/A'
            });

            updateTestStatus('✅ 调试信息已输出到控制台');

        } catch (error) {
            console.error('❌ 调试评论状态失败:', error);
            updateTestStatus(`❌ 调试失败: ${error.message}`);
        }
    }

    // 更新多视频处理状态显示
    function updateMultiVideoStatus(message) {
        // 添加多视频处理进度信息
        if (window.multiVideoState && window.multiVideoState.isMultiVideoMode) {
            const progressInfo = `[${window.multiVideoState.videosProcessed + 1}/${window.multiVideoState.totalVideos}] `;
            const fullMessage = progressInfo + message;
            updateTestStatus(fullMessage);
            //console.log('🎬 多视频状态:', fullMessage);
        } else {
            updateTestStatus(message);
        }
    }



    // 搜索功能
    function performSearch(keyword) {
        //console.log('开始搜索关键字：', keyword);

        // 保存搜索关键词到localStorage，用于后续返回搜索结果页面
        try {
            localStorage.setItem('lastSearchKeyword', keyword);
            //console.log('💾 搜索关键词已保存:', keyword);
        } catch (error) {
            //console.log('保存搜索关键词失败:', error);
        }

        try {
            // 使用新的搜索输入框选择器
            const searchInput = $('#douyin-header > div.oGNwsYVq > header > div > div > div.Zm5_td56 > div > div.zzJ4Uxm_.qgBoIOxC > div > div.qbeFLQBg > input');

            if (searchInput.length === 0) {
                // 尝试其他可能的搜索框选择器
                const alternativeSelectors = [
                    'input.st2xnJtZ.YIde9aUh[data-e2e="searchbar-input"]',
                    'input[data-e2e="searchbar-input"]',
                    'input[placeholder*="搜索你感兴趣的内容"]',
                    'input[placeholder*="搜索"]',
                    '#douyin-header input[type="text"]',
                    '.st2xnJtZ.YIde9aUh',
                    'input[maxlength="100"]',
                    '#douyin-header input'
                ];

                let found = false;
                for (let selector of alternativeSelectors) {
                    const altInput = $(selector);
                    if (altInput.length > 0) {
                        console.log('使用备用选择器找到搜索框：', selector);
                        performSearchWithInput(altInput, keyword);
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    throw new Error('未找到搜索输入框');
                }
            } else {
                console.log('使用新的主选择器找到搜索框');
                performSearchWithInput(searchInput, keyword);
            }
        } catch (error) {
            //console.log('搜索过程中出错：', error);

            // 向扩展发送搜索失败消息
            try {
                chrome.runtime.sendMessage({
                    action: 'searchResult',
                    success: false,
                    message: error.message
                });
            } catch (err) {
                //console.log('发送搜索失败消息时出错：', err);
            }
        }
    }

    // 使用指定的输入框执行搜索
    function performSearchWithInput(inputElement, keyword) {
        console.log('🔍 开始执行搜索步骤，关键词:', keyword);

        // 步骤1: 获取输入框焦点并等待
        console.log('📝 步骤1: 获取输入框焦点');
        inputElement.focus();
        inputElement[0].focus(); // 原生DOM方法

        // 等待焦点获取，增加延迟
        setTimeout(() => {
            console.log('📝 步骤2: 清空输入框内容');

            // 先清空输入框，确保清空完成
            inputElement.val('');
            inputElement[0].value = '';

            // 触发清空事件
            inputElement.trigger('input');
            inputElement.trigger('change');

            // 验证清空是否成功
            const currentValue = inputElement.val();
            console.log('📝 清空后输入框内容:', currentValue);

            // 等待清空完成后再输入
            setTimeout(() => {
                console.log('📝 步骤3: 输入搜索关键词 -', keyword);

                // 输入新的关键字
                inputElement.val(keyword);
                inputElement[0].value = keyword;

                // 触发输入相关事件
                inputElement.trigger('input');
                inputElement.trigger('change');
                inputElement.trigger('keyup');

                // 触发原生事件
                const inputEvent = new Event('input', { bubbles: true });
                const changeEvent = new Event('change', { bubbles: true });
                inputElement[0].dispatchEvent(inputEvent);
                inputElement[0].dispatchEvent(changeEvent);

                // 验证输入是否成功
                const finalValue = inputElement.val();
                console.log('📝 输入完成，当前值:', finalValue);

                if (finalValue !== keyword) {
                    console.warn('⚠️ 输入值与期望不符，重试输入');
                    inputElement.val(keyword);
                    inputElement[0].value = keyword;
                }

                // 步骤4: 等待输入稳定后执行搜索
                setTimeout(() => {
                    console.log('🔍 步骤4: 执行搜索');

                    // 首先尝试点击搜索按钮
                    const searchButton = $('#douyin-header > div.oGNwsYVq > header > div > div > div.Zm5_td56 > div > div.zzJ4Uxm_.qgBoIOxC > div > button > span');

                    if (searchButton.length > 0) {
                        console.log('✅ 找到搜索按钮，点击搜索');
                        searchButton.click();
                    } else {
                        console.log('⚠️ 未找到搜索按钮，尝试回车键搜索');

                        // 创建回车键事件
                        const enterKeyDown = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true,
                            cancelable: true
                        });

                        const enterKeyUp = new KeyboardEvent('keyup', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true,
                            cancelable: true
                        });

                        // 触发回车键事件
                        inputElement[0].dispatchEvent(enterKeyDown);
                        inputElement[0].dispatchEvent(enterKeyUp);

                        // 也尝试jQuery方式
                        inputElement.trigger($.Event('keydown', { keyCode: 13, which: 13 }));
                        inputElement.trigger($.Event('keyup', { keyCode: 13, which: 13 }));
                    }

                    console.log('🔍 搜索指令已发送，直接点击第一个视频...');

                    // 步骤5: 搜索结果一直存在，直接点击第一个视频
                    setTimeout(() => {
                        console.log('🎯 开始点击第一个视频');
                        clickFirstVideo();
                    }, 2000); // 等待2秒确保搜索完成

                }, 500); // 等待输入稳定，增加到500ms

            }, 300); // 等待清空完成，增加到300ms

        }, 200); // 等待焦点获取，增加到200ms
    }



    // 点击用户账号名称然后点击评论标签进入评论页面
    function pressXKey() {
        console.log('🎯 准备点击用户账号名称然后点击评论标签进入评论页面...');

        // 步骤1: 查找并点击用户账号名称
        console.log('📝 步骤1: 查找用户账号名称元素');

        // 获取当前视频索引，用于动态选择用户账号名称元素
        const currentVideoIndex = window.multiVideoState ? window.multiVideoState.currentVideoIndex : 0;
        const accountNameElements = $('#video-info-wrap > div.video-info-detail.isVideoInfoOptimise > div > div.account > div.account-name.userAccountTextHover > span > span > span > span > span > span');

        console.log(`📊 找到 ${accountNameElements.length} 个用户账号名称元素`);

        if (accountNameElements.length > currentVideoIndex) {
            console.log(`✅ 点击第${currentVideoIndex + 1}个视频的用户账号名称`);
            accountNameElements[currentVideoIndex].click();
        } else {
            // 如果找不到对应索引的元素，使用第一个
            if (accountNameElements.length > 0) {
                console.log('⚠️ 使用第一个用户账号名称元素');
                accountNameElements[0].click();
            } else {
                console.warn('❌ 未找到用户账号名称元素，尝试备用选择器');

                // 尝试备用选择器
                const alternativeSelectors = [
                    '#video-info-wrap .account-name',
                    '#video-info-wrap .account .account-name',
                    '[data-e2e="video-account-name"]',
                    '.account-name'
                ];

                let found = false;
                for (const selector of alternativeSelectors) {
                    const elements = $(selector);
                    if (elements.length > 0) {
                        console.log(`✅ 使用备用选择器找到用户名: ${selector}`);
                        elements[0].click();
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    console.error('❌ 所有选择器都未找到用户账号名称元素');
                }
            }
        }

        // 步骤2: 等待用户详情页面加载后点击评论标签
        setTimeout(() => {
            console.log('💬 步骤2: 查找并点击评论标签');

            const commentTab = $('#semiTabcomment > span');
            console.log(`📊 找到 ${commentTab.length} 个评论标签元素`);

            if (commentTab.length > 0) {
                console.log('✅ 点击评论标签');
                commentTab.click();

                // 步骤3: 等待评论页面加载完成
                setTimeout(() => {
                    console.log('🚀 评论页面加载完成，开始批量收集用户信息');
                    startBatchUserInfoCollection();
                }, 2000); // 增加等待时间到2秒

            } else {
                console.warn('❌ 未找到评论标签，尝试备用方法');

                // 尝试备用选择器
                const alternativeCommentSelectors = [
                    '#semiTabcomment',
                    '[data-e2e="comment-tab"]',
                    '.semi-tab-comment',
                    'span:contains("评论")'
                ];

                let found = false;
                for (const selector of alternativeCommentSelectors) {
                    const elements = $(selector);
                    if (elements.length > 0) {
                        console.log(`✅ 使用备用选择器找到评论标签: ${selector}`);
                        elements[0].click();
                        found = true;

                        setTimeout(() => {
                            startBatchUserInfoCollection();
                        }, 2000);
                        break;
                    }
                }

                if (!found) {
                    console.error('❌ 所有选择器都未找到评论标签，直接尝试开始收集');
                    setTimeout(() => {
                        startBatchUserInfoCollection();
                    }, 1000);
                }
            }

        }, 2000); // 增加等待时间到2秒
    }

    // 批量获取评论用户信息
    function startBatchUserInfoCollection() {
        //console.log('🚀 开始批量获取评论用户信息...');
        //console.log('🔍 当前window.totalComments值:', window.totalComments);
        //console.log('🔍 当前window.totalComments类型:', typeof window.totalComments);

        // 从localStorage获取用户选择的评论数量，默认为2
        let commentCount = 2;

        // 首先检查window.totalComments是否已经被设置
        if (window.totalComments && window.totalComments > 2) {
            commentCount = window.totalComments;
            //console.log('📊 使用window.totalComments:', commentCount);
        } else {
            try {
                const storedOptions = localStorage.getItem('douyinSearchOptions');
                //console.log('📊 localStorage原始数据:', storedOptions);

                if (storedOptions) {
                    const options = JSON.parse(storedOptions);
                    // 统一只使用comment字段
                    commentCount = options.comment || 2;
                    //console.log('📊 从localStorage读取的配置:', options);
                    //console.log('📊 解析出的评论数量:', commentCount);
                } else {
                    //console.log('📊 localStorage中没有douyinSearchOptions数据');
                }
            } catch (error) {
                //console.log('获取评论数量设置失败，使用默认值2:', error);
            }
        }

        //console.log(`📊 准备获取 ${commentCount} 条评论的用户信息`);
        updateMultiVideoStatus(`准备获取 ${commentCount} 条评论的用户信息`);

        // 存储所有用户数据
        window.allUserData = [];
        window.currentCommentIndex = 1;
        window.totalComments = commentCount;
        window.dataUploadCounter = 0; // 初始化数据上传计数器

        // 初始化多视频处理状态（如果还未初始化）
        if (!window.multiVideoState) {
            window.multiVideoState = {
                currentVideoIndex: 0,        // 当前处理的视频索引（从0开始）
                totalVideos: 1,              // 总视频数量
                videosProcessed: 0,          // 已处理的视频数量
                isMultiVideoMode: false,     // 是否为多视频模式
                allVideosData: []            // 所有视频的用户数据集合
            };
        }

        // 设置全局进度监控，防止卡住
        console.log('🔧 启动进度监控机制');
        window.lastProgressTime = Date.now();

        // 清除之前的监控器（如果存在）
        if (window.progressMonitorInterval) {
            clearInterval(window.progressMonitorInterval);
        }

        window.progressMonitorInterval = setInterval(() => {
            const currentTime = Date.now();
            const timeSinceLastProgress = currentTime - window.lastProgressTime;

            // 如果超过45秒没有进度，强制继续
            if (timeSinceLastProgress > 45000) {
                console.log('⏰ 检测到进度停滞超过45秒，强制继续处理');

                // 获取当前应该处理的评论索引
                let currentIndex = window.currentCommentIndex || 1;

                // 强制处理下一条评论
                if (currentIndex < window.totalComments) {
                    console.log(`🔄 强制跳过第${currentIndex}条评论，继续处理第${currentIndex + 1}条`);
                    window.currentCommentIndex = currentIndex + 1;

                    // 更新进度时间
                    window.lastProgressTime = Date.now();

                    // 强制获取下一条评论
                    setTimeout(() => {
                        getCommentUserInfo(currentIndex + 1);
                    }, 1000);
                } else {
                    console.log('🏁 强制结束当前视频处理');
                    finishBatchCollection();
                }
            }
        }, 10000); // 每10秒检查一次

        // 开始获取第一个用户信息
        getCommentUserInfo(1);
    }

    // 获取指定索引评论的个人信息
    function getCommentUserInfo(commentIndex) {
        console.log(`🎯 开始获取第${commentIndex}条评论的个人信息...`);
        updateMultiVideoStatus(`正在获取第${commentIndex}条评论的个人信息`);

        // 更新进度时间
        window.lastProgressTime = Date.now();
        window.currentCommentIndex = commentIndex;

        // 清除之前的超时检测
        if (window.currentCommentTimeout) {
            clearTimeout(window.currentCommentTimeout);
            window.currentCommentTimeout = null;
        }

        // 添加调试信息
        console.log(`📊 当前状态: 视频${window.multiVideoState ? window.multiVideoState.currentVideoIndex + 1 : 1}, 评论${commentIndex}/${window.totalComments}`);

        // 检查是否出现了评论结束标识元素
        const endMarkerSelector = '#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div.fanRMYie';
        const endMarkerElement = $(endMarkerSelector);

        let endMarkerDetectionCount = 0; // 初始化检测计数器

        function checkEndMarker() {
            if (endMarkerElement.length > 0) {
                console.log('🏁 检测到评论结束标识元素(div.fanRMYie)，当前视频评论已获取完毕');
                updateMultiVideoStatus('检测到评论结束标识，当前视频评论已获取完毕');

                // 增加计数器
                endMarkerDetectionCount++;

                // 如果检测次数未达三次，则直接返回，等待下次检查
                if (endMarkerDetectionCount < 3) {
                    console.log(`已检测到结束标识 ${endMarkerDetectionCount} 次，还需检测 ${3 - endMarkerDetectionCount} 次`);
                    return;
                }

                // 如果是多视频模式，转到下一个视频
                if (window.multiVideoState && window.multiVideoState.isMultiVideoMode) {
                    console.log('📹 多视频模式：准备处理下一个视频');
                    finishBatchCollection(); // 完成当前视频的收集
                    return;
                } else {
                    // 单视频模式，直接完成收集
                    console.log('📹 单视频模式：完成收集');
                    finishBatchCollection();
                    return;
                }
            } else {
                console.log('未检测到评论结束标识，继续...');
            }
        }

        // 调用此函数进行检测，你可以设置定时调用或在适当的地方调用它三次
        checkEndMarker();

        // 动态生成评论头像选择器
        const commentAvatarSelector = `#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(${commentIndex}) > div > div.VPtAXFCJ.comment-item-avatar > div > a > span > img`;

        const avatarElement = $(commentAvatarSelector);

        if (avatarElement.length > 0) {
            //console.log(`✅ 找到第${commentIndex}条评论的头像元素`);

            // 获取头像图片的src属性
            const avatarSrc = avatarElement.attr('src') || avatarElement.prop('src') || '';
            //console.log(`🖼️ 第${commentIndex}条评论的头像URL:`, avatarSrc);

            // 保存头像URL到localStorage
            localStorage.setItem('currentAvatarSrc', avatarSrc);

            // 保存当前处理的评论索引
            localStorage.setItem('currentCommentIndex', commentIndex.toString());

            // 标记用户详情页面即将打开
            markUserDetailPageOpened();

            // 点击头像，跳转到个人详情页面
            avatarElement.click();

            //console.log(`🔗 已点击第${commentIndex}条评论头像，等待跳转到个人详情页面...`);

            //console.log(`✅ 已点击第${commentIndex}条评论头像，跳转到个人详情页面`);

        } else {
            //console.log(`❌ 未找到第${commentIndex}条评论的头像元素`);

            // 尝试滑动评论区加载更多评论
            //console.log('📜 尝试滑动评论区加载更多评论...');
            scrollCommentArea();

            // 等待滑动后重新尝试
            setTimeout(() => {
                // 滑动后再次检查是否出现了评论结束标识元素
                const endMarkerElement = $(endMarkerSelector);
                if (endMarkerElement.length > 0) {
                    console.log('🏁 滑动后检测到评论结束标识元素(div.fanRMYie)，当前视频评论已获取完毕');
                    updateMultiVideoStatus('滑动后检测到评论结束标识，当前视频评论已获取完毕');
                    finishBatchCollection();
                    return;
                }

                const retryAvatarElement = $(commentAvatarSelector);
                if (retryAvatarElement.length > 0) {
                    //console.log(`✅ 滑动后找到第${commentIndex}条评论的头像元素`);

                    // 获取头像图片的src属性
                    const retryAvatarSrc = retryAvatarElement.attr('src') || retryAvatarElement.prop('src') || '';
                    //console.log(`🖼️ 滑动后第${commentIndex}条评论的头像URL:`, retryAvatarSrc);

                    // 保存头像URL到localStorage
                    localStorage.setItem('currentAvatarSrc', retryAvatarSrc);

                    // 保存当前处理的评论索引
                    localStorage.setItem('currentCommentIndex', commentIndex.toString());

                    // 标记用户详情页面即将打开
                    markUserDetailPageOpened();

                    // 点击头像，跳转到个人详情页面
                    retryAvatarElement.click();

                    //console.log(`🔗 已点击第${commentIndex}条评论头像，等待跳转到个人详情页面...`);

                } else {
                    console.log(`❌ 滑动后仍未找到第${commentIndex}条评论，跳过此条`);

                    // 检查当前可见的评论数量
                    const totalVisibleComments = $('#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div').length;
                    console.log(`📊 当前可见评论数量: ${totalVisibleComments}, 尝试获取第${commentIndex}条`);

                    // 如果当前评论索引超过了可见评论数量太多，可能已经没有更多评论了
                    if (commentIndex > totalVisibleComments + 1) {
                        console.log(`⚠️ 评论索引${commentIndex}超过可见评论数${totalVisibleComments}，可能已无更多评论`);
                        console.log('🏁 提前结束当前视频的评论收集');
                        finishBatchCollection();
                        return;
                    }

                    // 如果滑动后还是找不到，尝试下一条
                    if (commentIndex < window.totalComments) {
                        console.log(`⏭️ 跳过第${commentIndex}条评论，尝试第${commentIndex + 1}条`);
                        setTimeout(() => {
                            getCommentUserInfo(commentIndex + 1);
                        }, 1000);
                    } else {
                        console.log('🏁 所有评论处理完成');
                        finishBatchCollection();
                    }
                }
            }, 2000); // 恢复到2秒
        }
    }

    // 滑动评论区加载更多评论
    function scrollCommentArea() {
        //console.log('📜 开始滑动评论区...');

        // 评论区容器选择器
        const commentContainerSelector = '#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV';
        const commentContainer = $(commentContainerSelector);

        if (commentContainer.length > 0) {
            //console.log('✅ 找到评论区容器，开始滑动');

            // 获取评论区元素
            const container = commentContainer[0];

            // 记录当前滚动位置
            const currentScrollTop = container.scrollTop;
            //console.log('📊 当前滚动位置:', currentScrollTop);

            // 先往上滑动50px
            container.scrollTop = Math.max(0, currentScrollTop - 100);
            //console.log('📜 先往上滑动50px，位置:', container.scrollTop);

            // 等待100ms后再往下滑动300px
            setTimeout(() => {
                const scrollDistance = 400;
                container.scrollTop = currentScrollTop + scrollDistance;
                //console.log(`📜 再往下滑动${scrollDistance}px，新位置:`, container.scrollTop);

                // 触发滚动事件
                const scrollEvent = new Event('scroll', { bubbles: true });
                container.dispatchEvent(scrollEvent);

                // 模拟鼠标滚轮事件
                const wheelEvent = new WheelEvent('wheel', {
                    deltaY: scrollDistance,
                    bubbles: true,
                    cancelable: true
                });
                container.dispatchEvent(wheelEvent);
            }, 500); // 恢复到500毫秒

        } else {
            //console.log('❌ 未找到评论区容器');

            // 尝试滑动整个页面
            //console.log('📜 尝试滑动整个页面...');
            window.scrollBy(0, -100); // 先往上滑动50px
            setTimeout(() => {
                window.scrollBy(0, 300); // 再往下滑动300px
            }, 500); // 恢复到500毫秒
        }
    }



    // 获取用户详情信息（在个人详情页面使用）
    function getUserDetailInfo() {
        // 首先检查是否存在需要跳过的元素
        const skipElementSelector = '#douyin-right-container > div.parent-route-container.XoIW2IMs.route-scroll-container.IhmVuo1S > div > div.UCMChFh2';
        const skipElement = $(skipElementSelector);

        if (skipElement.length > 0) {
            window.close(); // 关闭当前用户详情页面
            return null; // 返回null表示跳过此用户
        }

        console.log('✅ 未检测到跳过元素，继续正常获取用户信息');

        // 点击编辑标签获取编辑内容
        $('#semiTabcompilation > div > span').click();

        // 等待编辑内容加载
        setTimeout(() => {
            var $elements = $('#user_detail_element > div > div.XA9ZQ2av > div > div > div.z_YvCWYy.Klp5EcJu > div > div.pCVdP6Bb.T9ZSs8iN > div > div > ul > li > div > div > a > div > div.XjTeAr5Q > div > div');

            let compilationsName = '';
            if ($elements.length > 0) {
                let names = [];
                $elements.each(function () {
                    let text = $(this).text().trim();
                    if (text) {
                        names.push(text);
                    }
                });
                compilationsName = names.join(' - ');
                console.log('✅ 获取到编辑内容:', compilationsName);
            } else {
                console.log('❌ 未找到编辑内容');
            }

            // 保存编辑内容
            localStorage.setItem('dy_compilations_name', compilationsName);
        }, 2000);

        // 使用稳定的容器和data-e2e属性进行信息提取
        const container = $('#user_detail_element');
        if (container.length === 0) {
            console.error('❌ 无法找到 #user_detail_element 容器，提取失败');
            setTimeout(() => {
                window.close(); // 延迟关闭页面
            }, 2000);
            return null;
        }

        console.log('✅ 找到用户详情容器，开始使用稳定选择器提取信息');

        // 使用稳定的data-e2e属性和选择器提取用户信息
        const userData = {
            username: container.find('[data-e2e="user-info"] h1').text().trim(), // 修改为username以匹配后续API
            nickname: container.find('[data-e2e="user-info"] h1').text().trim(), // 保留nickname以兼容
            userBio: container.find('.lFECd241').text().trim(), // 修改为userBio以匹配后续API
            signature: container.find('.lFECd241').text().trim(), // 保留signature以兼容
            followingCount: container.find('[data-e2e="user-info-follow"] .C1cxu0Vq').text().trim(), // 修改为匹配API
            following: container.find('[data-e2e="user-info-follow"] .C1cxu0Vq').text().trim(), // 保留以兼容
            followersCount: container.find('[data-e2e="user-info-fans"] .C1cxu0Vq').text().trim(), // 修改为匹配API
            fans: container.find('[data-e2e="user-info-fans"] .C1cxu0Vq').text().trim(), // 保留以兼容
            likesCount: container.find('[data-e2e="user-info-like"] .C1cxu0Vq').text().trim(), // 修改为匹配API
            likes: container.find('[data-e2e="user-info-like"] .C1cxu0Vq').text().trim(), // 保留以兼容
            douyinId: container.find('.OcCvtZ2a').contents().filter(function () { return this.nodeType === 3; }).text().trim(),
            userLocation: container.find('.DtUnx4ER').text().trim(), // 修改为匹配API
            ipLocation: container.find('.DtUnx4ER').text().trim(), // 保留以兼容
            locationTag: container.find('.YcpSmZeQ').eq(1).text().trim(),
            avatarUrl: container.find('[data-e2e="live-avatar"] img').attr('src') || container.find('[data-e2e="live-avatar"] img').prop('src') || '',
            compilationsName: localStorage.getItem('dy_compilations_name') || '', // 添加编辑内容

            // 【新增】提取年龄
            age: container.find('[data-e2e="user-info"] .YcpSmZeQ').first().text().trim(),

            // 【新增】提取背景图div的style
            userBackground: extractBackgroundStyle(container.find('div[style*="background-image"]'))
        };

        // 提取背景图div的style的辅助函数
        function extractBackgroundStyle(element) {
            if (element.length === 0) {
                console.log('⚠️ 未找到背景图元素');
                return '';
            }

            try {
                // 直接获取style属性
                const styleAttr = element.attr('style');
                if (styleAttr) {
                    console.log('✅ 成功提取到背景图div的style:', styleAttr);
                    return styleAttr;
                }

                console.log('⚠️ 背景图元素没有style属性');
                return '';
            } catch (error) {
                console.error('❌ 提取背景图style时出错:', error);
                return '';
            }
        }

        // 验证是否获取到有效信息
        if (!userData.username && !userData.douyinId) {
            console.log("未能提取到有效的用户昵称或ID，视为获取失败。");
            return null;
        }

        // 保存用户数据到localStorage
        try {
            localStorage.setItem('douyinUserInfo', JSON.stringify(userData));
            console.log('✅ 用户详情信息已保存到localStorage:', userData);
        } catch (error) {
            console.error('❌ 保存用户详情信息失败:', error);
        }

        return userData;
    }

    // 获取用户评论内容
    function getUserComment() {
        console.log('💬 开始获取用户评论内容...');

        // 从localStorage获取用户信息、当前评论索引和头像URL
        let userInfo = null;
        let commentIndex = 1;
        let avatarSrc = '';
        try {
            const storedUserInfo = localStorage.getItem('douyinUserInfo');
            if (storedUserInfo) {
                userInfo = JSON.parse(storedUserInfo);
                console.log('📋 从localStorage获取到用户信息:', userInfo);
            } else {
                console.log('⚠️ localStorage中没有找到用户信息');
            }

            const storedIndex = localStorage.getItem('currentCommentIndex');
            if (storedIndex) {
                commentIndex = parseInt(storedIndex);
                console.log(`📍 当前处理第${commentIndex}条评论`);
            } else {
                console.log('⚠️ 未找到评论索引，使用默认值1');
            }

            const storedAvatarSrc = localStorage.getItem('currentAvatarSrc');
            if (storedAvatarSrc) {
                avatarSrc = storedAvatarSrc;
                console.log(`🖼️ 从评论页面获取到头像URL:`, avatarSrc);
            } else {
                console.log('⚠️ 未找到头像URL');
            }
        } catch (error) {
            console.error('❌ 从localStorage获取信息失败:', error);
        }

        // 动态生成用户评论选择器
        const userCommentSelector = `#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(${commentIndex}) > div > div.VjrdhTqP > div > div.LvAtyU_f > span > span > span > span > span > span > span`;

        // 添加作者赞过标记的选择器
        const authorLikedSelector = `#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(${commentIndex}) > div > div.VjrdhTqP > div > div.LvAtyU_f > div > div > span`;

        // 检测是否被作者赞过
        const authorLikedElement = $(authorLikedSelector);
        const isLikedByAuthor = authorLikedElement.length > 0 ? 1 : 0;
        console.log(`📊 评论${commentIndex}是否被作者赞过: ${isLikedByAuthor ? '是' : '否'}`);

        const commentElement = $(userCommentSelector);

        if (commentElement.length > 0) {
            const commentText = commentElement.text().trim();
            console.log('✅ 获取到用户评论内容:', commentText);

            // 获取搜索关键词
            let searchKeyword = '';
            try {
                searchKeyword = localStorage.getItem('lastSearchKeyword') || '';
                console.log('📝 获取到搜索关键词:', searchKeyword);
            } catch (error) {
                console.error('❌ 获取搜索关键词失败:', error);
            }

            // 如果没有从localStorage获取到用户信息，尝试构建一个基本的用户信息对象
            if (!userInfo) {
                console.log('⚠️ 没有用户详情信息，创建基本信息对象');
                userInfo = {
                    username: '未获取到用户名',
                    douyinId: '未获取到抖音ID',
                    avatarUrl: avatarSrc || '',
                };
            }

            // 合并用户信息和评论内容
            const completeUserData = {
                ...(userInfo || {}), // 安全展开用户详情信息
                userComment: commentText, // 添加评论内容
                commentIndex: commentIndex, // 添加评论索引
                commentTimestamp: new Date().toLocaleString(), // 添加评论获取时间
                avatarUrl: (userInfo && userInfo.avatarUrl) || avatarSrc, // 安全访问avatarUrl属性
                searchKeyword: searchKeyword, // 添加搜索关键词
                isLikedByAuthor: isLikedByAuthor // 添加作者赞过标记 (1=赞过, 0=未赞)
            };

            console.log(`🎯 第${commentIndex}条评论的完整用户数据:`, completeUserData);

            // 立即保存到本地存储
            const storageKey = LocalDataManager.saveUserData(completeUserData);
            if (storageKey) {
                console.log(`✅ 第${commentIndex}条评论数据已保存到本地存储，键名: ${storageKey}`);

                // 增加上传计数器
                window.dataUploadCounter = (window.dataUploadCounter || 0) + 1;
                console.log(`📊 当前数据上传计数: ${window.dataUploadCounter}`);

                // 每50条数据自动上传一次
                if (window.dataUploadCounter >= 50) {
                    console.log(`📤 已收集${window.dataUploadCounter}条数据，开始自动上传...`);
                    window.dataUploadCounter = 0; // 重置计数器

                    // 异步上传数据，不阻塞主流程
                    DataUploadManager.uploadAllStoredData()
                        .then((result) => {
                            console.log('📊 自动上传结果:', result);
                            if (result.success) {
                                console.log('✅ 50条数据自动上传成功');
                                // 上传成功后清理本地存储
                                LocalDataManager.clearAllData();
                                console.log('🧹 本地存储数据已清理');
                            } else {
                                console.log(`⚠️ 部分数据上传失败，${result.failed} 条数据保留在本地存储`);
                                // 只清理成功上传的数据
                                if (result.succeeded > 0) {
                                    console.log(`🧹 清理已成功上传的 ${result.succeeded} 条数据`);
                                    DataUploadManager.clearSuccessfullyUploadedData();
                                }
                            }
                        })
                        .catch((error) => {
                            console.error('❌ 自动上传过程出错:', error);
                            // 即使出错,也尝试清理已成功上传的数据(如果有)
                            if (DataUploadManager.successfullyUploadedKeys &&
                                DataUploadManager.successfullyUploadedKeys.length > 0) {
                                console.log('⚠️ 尝试清理已上传成功的部分数据');
                                DataUploadManager.clearSuccessfullyUploadedData();
                            }
                        });
                }
            } else {
                console.error('❌ 保存到本地存储失败');
            }

            // 将用户数据添加到全局数组（保持兼容性）
            if (!window.allUserData) {
                window.allUserData = [];
            }
            window.allUserData.push(completeUserData);
            console.log(`📊 全局数据数组现有 ${window.allUserData.length} 条数据`);

            // 清理当前用户信息
            try {
                localStorage.removeItem('douyinUserInfo');
                localStorage.removeItem('currentCommentIndex');
                localStorage.removeItem('currentAvatarSrc');
                console.log('🧹 已清理当前用户相关的localStorage数据');
            } catch (error) {
                console.error('❌ 清理localStorage失败:', error);
            }

            // 处理下一条评论
            processNextComment(commentIndex);

            return completeUserData;
        } else {
            console.log('❌ 未找到用户评论元素，尝试备用选择器');

            // 尝试备用选择器
            const altSelectors = [
                `#merge-all-comment-container > div > div.HV3aiR5J.comment-mainContent.iV2CcAAV > div:nth-child(${commentIndex}) div.LvAtyU_f span`,
                `#merge-all-comment-container div:nth-child(${commentIndex}) .LvAtyU_f`,
                `#merge-all-comment-container div:nth-child(${commentIndex}) [data-e2e="comment-content"]`
            ];

            let commentText = '未找到评论内容';
            for (const selector of altSelectors) {
                const altElement = $(selector);
                if (altElement.length > 0) {
                    commentText = altElement.text().trim();
                    console.log(`✅ 使用备用选择器获取到评论内容: ${commentText}`);
                    break;
                }
            }

            // 即使没有评论，也记录用户信息
            if (userInfo) {
                // 获取搜索关键词
                let searchKeyword = '';
                try {
                    searchKeyword = localStorage.getItem('lastSearchKeyword') || '';
                } catch (error) {
                    console.error('获取搜索关键词失败:', error);
                }

                const completeUserData = {
                    ...userInfo,
                    userComment: commentText,
                    commentIndex: commentIndex,
                    commentTimestamp: new Date().toLocaleString(),
                    searchKeyword: searchKeyword,
                    isLikedByAuthor: isLikedByAuthor // 添加作者赞过标记
                };

                console.log('✅ 即使未找到评论元素，仍构建了完整用户数据:', completeUserData);

                // 立即保存到本地存储
                const storageKey = LocalDataManager.saveUserData(completeUserData);
                if (storageKey) {
                    console.log(`✅ 用户数据已保存到本地存储，键名: ${storageKey}`);
                }

                // 将用户数据添加到全局数组
                if (!window.allUserData) {
                    window.allUserData = [];
                }
                window.allUserData.push(completeUserData);

                // 清理localStorage
                try {
                    localStorage.removeItem('douyinUserInfo');
                    localStorage.removeItem('currentCommentIndex');
                    localStorage.removeItem('currentAvatarSrc');
                } catch (error) {
                    console.error('清理localStorage失败:', error);
                }

                // 处理下一条评论
                processNextComment(commentIndex);

                return completeUserData;
            } else {
                console.log('❌ 未找到用户信息和评论内容，继续处理下一条评论');

                // 清理localStorage
                try {
                    localStorage.removeItem('douyinUserInfo');
                    localStorage.removeItem('currentCommentIndex');
                    localStorage.removeItem('currentAvatarSrc');
                } catch (error) {
                    console.error('清理localStorage失败:', error);
                }

                // 获取当前评论索引
                let currentCommentIndex = 1;
                try {
                    const storedIndex = localStorage.getItem('currentCommentIndex');
                    if (storedIndex) {
                        currentCommentIndex = parseInt(storedIndex);
                    }
                } catch (error) {
                    console.error('获取评论索引失败:', error);
                }

                // 继续处理下一条评论
                console.log(`🔄 用户信息获取失败，跳过第${currentCommentIndex}条评论，继续处理下一条`);
                processNextComment(currentCommentIndex);

                return null;
            }
        }
    }

    // 处理下一条评论
    function processNextComment(currentIndex) {
        // 清除评论处理超时
        if (window.currentCommentProcessTimeout) {
            clearTimeout(window.currentCommentProcessTimeout);
            window.currentCommentProcessTimeout = null;
        }

        const totalComments = window.totalComments || 2;
        console.log('📊 processNextComment调试信息:', {
            currentIndex: currentIndex,
            'window.totalComments': window.totalComments,
            totalComments: totalComments,
            '条件检查': currentIndex < totalComments
        });

        if (currentIndex < totalComments) {
            const nextIndex = currentIndex + 1;
            console.log(`⏭️ 准备处理第${nextIndex}条评论`);

            // 设置超时检测，防止卡住
            const timeoutId = setTimeout(() => {
                console.log(`⏰ 第${nextIndex}条评论处理超时，强制跳过`);
                if (nextIndex < totalComments) {
                    getCommentUserInfo(nextIndex + 1);
                } else {
                    console.log('🏁 超时后所有评论处理完成');
                    finishBatchCollection();
                }
            }, 30000); // 30秒超时

            // 保存超时ID，以便在正常处理时清除
            window.currentCommentTimeout = timeoutId;

            // 等待一段时间后处理下一条评论
            setTimeout(() => {
                getCommentUserInfo(nextIndex);
            }, 3000); // 恢复到3秒
        } else {
            console.log('🏁 所有评论处理完成');
            finishBatchCollection();
        }
    }

    // 清除任务相关的缓存
    function clearTaskCache() {
        console.log('🧹 开始清除任务相关的缓存...');

        try {
            // 清除localStorage中的任务相关数据
            const keysToRemove = [
                'douyinUserInfo',
                'currentCommentIndex',
                'currentAvatarSrc',
                'lastSearchKeyword',
                'userDetailPageOpened',
                'batchCollectionActive',
                'multiVideoProcessing'
                // 注意：currentTaskId不在这里清理，只在任务真正完成后才清理
            ];

            keysToRemove.forEach(key => {
                try {
                    localStorage.removeItem(key);
                    console.log(`✅ 已清除localStorage: ${key}`);
                } catch (error) {
                    console.log(`⚠️ 清除localStorage失败: ${key}`, error);
                }
            });

            // 重置全局变量但保留taskId和receiverName
            if (window.apiTaskInfo) {
                const savedTaskId = window.apiTaskInfo.taskId;
                const savedReceiverName = window.apiTaskInfo.receiverName;

                // 临时存储任务ID和接收者名称
                console.log('🔄 保留从API接收的任务ID:', savedTaskId);

                // 重置其他变量
                window.apiTaskInfo.queryValue = '';
                window.apiTaskInfo.isTaskActive = false;
                window.apiTaskInfo.isScriptRunning = false;

                // 恢复任务ID和接收者名称
                if (savedTaskId) {
                    window.apiTaskInfo.taskId = savedTaskId; // 恢复从API接收的ID
                }
                window.apiTaskInfo.receiverName = savedReceiverName || 'abcdefg';

                console.log('✅ 已重置任务信息(保留API任务ID):', window.apiTaskInfo);
            }

            // 清除多视频状态
            if (window.multiVideoState) {
                window.multiVideoState = {
                    currentVideoIndex: 0,
                    totalVideos: 1,
                    videosProcessed: 0,
                    isMultiVideoMode: false,
                    allVideosData: []
                };
                console.log('✅ 已重置多视频状态');
            }

            // 清除其他全局变量
            window.allUserData = [];
            window.currentCommentIndex = 1;
            window.totalComments = 2;
            window.dataUploadCounter = 0;

            console.log('✅ 任务缓存清理完成');

        } catch (error) {
            console.error('❌ 清除任务缓存时出错:', error);
        }
    }

    // 清理任务ID相关缓存（只在任务真正完成后调用）
    function clearTaskIdCache() {
        console.log('🧹 开始清理任务ID相关缓存...');

        try {
            // 清理localStorage中的taskId
            localStorage.removeItem('currentTaskId');
            console.log('✅ 已清除localStorage中的currentTaskId');

            // 清理全局变量中的taskId
            if (window.apiTaskInfo) {
                window.apiTaskInfo.taskId = '';
                console.log('✅ 已清除全局变量中的taskId');
            }

            console.log('🧹 任务ID缓存清理完成');
        } catch (error) {
            console.error('❌ 清理任务ID缓存失败:', error);
        }
    }

    // 完成批量收集
    function finishBatchCollection() {
        console.log('🎉 当前视频的用户信息收集完成！');
        console.log('📊 收集到的当前视频用户数据:', window.allUserData);

        // 清理进度监控器
        if (window.progressMonitorInterval) {
            clearInterval(window.progressMonitorInterval);
            window.progressMonitorInterval = null;
            console.log('🔧 已清理进度监控器');
        }

        // 如果是多视频模式，保存当前视频的数据
        if (window.multiVideoState && window.multiVideoState.isMultiVideoMode) {
            // 将当前视频的数据添加到总数据集合中
            const currentVideoData = {
                videoIndex: window.multiVideoState.currentVideoIndex,
                videoData: [...window.allUserData] // 复制当前视频的用户数据
            };
            window.multiVideoState.allVideosData.push(currentVideoData);
            window.multiVideoState.videosProcessed++;

            //console.log(`✅ 第${window.multiVideoState.currentVideoIndex + 1}个视频处理完成`);
            //console.log(`📊 已处理 ${window.multiVideoState.videosProcessed}/${window.multiVideoState.totalVideos} 个视频`);
            updateMultiVideoStatus(`第${window.multiVideoState.currentVideoIndex + 1}个视频处理完成`);

            // 检查是否还有更多视频需要处理
            if (window.multiVideoState.videosProcessed < window.multiVideoState.totalVideos) {
                //console.log('🔄 准备处理下一个视频...');
                updateMultiVideoStatus('准备处理下一个视频...');

                // 清理当前视频的数据，准备处理下一个视频
                window.allUserData = [];
                window.currentCommentIndex = 1;

                // 返回搜索结果页面，然后处理下一个视频
                returnToSearchResults();
            } else {
                //console.log('🎉 所有视频处理完成！');
                finishAllVideosCollection();
            }
        } else {
            // 单视频模式，直接完成
            finishSingleVideoCollection();
        }
    }

    // 完成单个视频收集（原有逻辑）
    function finishSingleVideoCollection() {
        //console.log('🎉 单视频用户信息收集完成！');

        // 发送所有用户数据
        try {
            chrome.runtime.sendMessage({
                action: 'allUserDataCollected',
                success: true,
                allUserData: window.allUserData,
                totalCount: window.allUserData ? window.allUserData.length : 0,
                message: `批量收集完成，共获取${window.allUserData ? window.allUserData.length : 0}个用户的信息`
            });
        } catch (error) {
            //console.log('发送所有用户数据消息失败:', error);
        }

        // 🚀 脚本完全执行完成，现在上传所有本地存储的数据
        console.log('🏁 单视频脚本执行完成，开始上传本地存储的数据...');

        // 显示本地存储统计
        const stats = LocalDataManager.getStorageStats();
        console.log('📊 本地存储统计:', stats);

        // 上传所有数据
        DataUploadManager.uploadAllStoredData()
            .then((result) => {
                console.log('📊 数据上传结果:', result);

                if (result.success) {
                    console.log('✅ 所有数据上传成功');
                    // 全部成功时清理所有缓存
                    LocalDataManager.clearAllData();
                    console.log('🧹 已清空所有本地存储数据');
                } else {
                    console.log(`⚠️ 部分数据上传失败，${result.failed} 条数据保留在本地存储`);
                    // 部分失败时只清理成功上传的数据
                    if (result.succeeded > 0) {
                        console.log(`🧹 清理已成功上传的 ${result.succeeded} 条数据`);
                        DataUploadManager.clearSuccessfullyUploadedData();
                    }
                }

                // 调用任务完成接口
                if (window.apiTaskInfo && window.apiTaskInfo.taskId) {
                    console.log('🏁 准备调用任务完成接口...', {
                        taskId: window.apiTaskInfo.taskId,
                        apiService存在: !!window.apiService,
                        apiService方法: window.apiService ? Object.keys(window.apiService) : 'N/A',
                        finishTask方法存在: window.apiService && typeof window.apiService.finishTask === 'function',
                        当前时间: new Date().toLocaleTimeString()
                    });

                    // 提供更直观的跟踪标记
                    console.log('🚩 ---开始任务完成标记流程(单视频)---');

                    // 使用api-service.js中的finishTask函数
                    let finishTaskPromise;
                    if (typeof window.apiService !== 'undefined' && window.apiService.finishTask) {
                        finishTaskPromise = window.apiService.finishTask(window.apiTaskInfo.taskId);
                    } else if (typeof finishTask === 'function') {
                        finishTaskPromise = finishTask(window.apiTaskInfo.taskId);
                    } else {
                        console.error('❌ finishTask函数不可用');
                        return;
                    }

                    finishTaskPromise.then((result) => {
                        console.log('✅ 任务完成标记成功', result);
                        console.log('🎯 单视频任务完成 - 任务ID:', window.apiTaskInfo.taskId);

                        // 任务完成后清理taskId缓存
                        clearTaskIdCache();

                        // 清除任务相关的缓存
                        clearTaskCache();
                    })
                        .catch((error) => {
                            console.error('❌ 任务完成标记失败:', error);
                            // 即使标记失败也要清除缓存
                            clearTaskCache();
                        });
                } else {
                    console.log('⚠️ 未找到任务ID，跳过任务完成标记', {
                        apiTaskInfo: window.apiTaskInfo
                    });
                    // 没有任务ID也要清除缓存
                    clearTaskCache();
                }
            })
            .catch((error) => {
                console.error('❌ 数据上传过程出错:', error);
            });

        // 清理全局变量
        window.allUserData = [];
        window.currentCommentIndex = 1;
        window.totalComments = 2;
        window.dataUploadCounter = 0;

        // 重置脚本运行状态并处理下一个任务（只调用一次）
        setTimeout(() => {
            if (window.apiService && window.apiService.restartTaskCheckerAfterCompletion) {
                console.log('🔄 单视频任务完成，调用任务完成处理函数...');
                window.apiService.restartTaskCheckerAfterCompletion();
            }
        }, 5000); // 等待5秒后处理下一个任务
    }

    // 完成所有视频收集
    function finishAllVideosCollection() {
        //console.log('🎉 所有视频的用户信息收集完成！');

        // 合并所有视频的数据
        const allUsersData = [];
        window.multiVideoState.allVideosData.forEach((videoData, index) => {
            videoData.videoData.forEach(userData => {
                allUsersData.push({
                    ...userData,
                    sourceVideoIndex: index + 1 // 添加来源视频索引
                });
            });
        });

        //console.log('📊 所有视频的用户数据:', allUsersData);

        // 发送所有视频的用户数据
        try {
            chrome.runtime.sendMessage({
                action: 'allUserDataCollected',
                success: true,
                allUserData: allUsersData,
                totalCount: allUsersData.length,
                videosProcessed: window.multiVideoState.videosProcessed,
                message: `多视频收集完成，共处理${window.multiVideoState.videosProcessed}个视频，获取${allUsersData.length}个用户的信息`
            });
        } catch (error) {
            //console.log('发送所有视频用户数据消息失败:', error);
        }

        // 🚀 脚本完全执行完成，现在上传所有本地存储的数据
        console.log('🏁 多视频脚本执行完成，开始上传本地存储的数据...');

        // 显示本地存储统计
        const stats = LocalDataManager.getStorageStats();
        console.log('📊 本地存储统计:', stats);

        // 上传所有数据
        DataUploadManager.uploadAllStoredData()
            .then((result) => {
                console.log('📊 数据上传结果:', result);

                if (result.success) {
                    console.log('✅ 所有数据上传成功，清理本地存储');
                    LocalDataManager.clearAllData();
                } else {
                    console.log(`⚠️ 部分数据上传失败，${result.failed} 条数据保留在本地存储`);
                    // 部分失败时只清理成功上传的数据
                    if (result.succeeded > 0) {
                        console.log(`🧹 清理已成功上传的 ${result.succeeded} 条数据`);
                        DataUploadManager.clearSuccessfullyUploadedData();
                    }
                }

                // 调用任务完成接口
                if (window.apiTaskInfo && window.apiTaskInfo.taskId) {
                    console.log('🏁 准备调用任务完成接口...', {
                        taskId: window.apiTaskInfo.taskId,
                        apiService存在: !!window.apiService,
                        apiService方法: window.apiService ? Object.keys(window.apiService) : 'N/A',
                        finishTask方法存在: window.apiService && typeof window.apiService.finishTask === 'function',
                        当前时间: new Date().toLocaleTimeString()
                    });

                    // 提供更直观的跟踪标记
                    console.log('🚩 ---开始任务完成标记流程(多视频)---');

                    // 使用api-service.js中的finishTask函数
                    let finishTaskPromise;
                    if (typeof window.apiService !== 'undefined' && window.apiService.finishTask) {
                        finishTaskPromise = window.apiService.finishTask(window.apiTaskInfo.taskId);
                    } else if (typeof finishTask === 'function') {
                        finishTaskPromise = finishTask(window.apiTaskInfo.taskId);
                    } else {
                        console.error('❌ finishTask函数不可用');
                        return;
                    }

                    finishTaskPromise.then((result) => {
                        console.log('✅ 任务完成标记成功', result);
                        console.log('🎯 多视频任务完成 - 任务ID:', window.apiTaskInfo.taskId);

                        // 任务完成后清理taskId缓存
                        clearTaskIdCache();

                        // 清除任务相关的缓存
                        clearTaskCache();
                    })
                        .catch((error) => {
                            console.error('❌ 任务完成标记失败:', error);
                            // 即使标记失败也要清除缓存
                            clearTaskCache();
                        });
                } else {
                    console.log('⚠️ 未找到任务ID，跳过任务完成标记', {
                        apiTaskInfo: window.apiTaskInfo
                    });
                    // 没有任务ID也要清除缓存
                    clearTaskCache();
                }

            })
            .catch((error) => {
                console.error('❌ 数据上传过程出错:', error);
            })
            .finally(() => {
                // 无论成功还是失败，都要处理下一个任务（只调用一次）
                setTimeout(() => {
                    if (window.apiService && window.apiService.restartTaskCheckerAfterCompletion) {
                        console.log('🔄 多视频任务完成，调用任务完成处理函数...');
                        window.apiService.restartTaskCheckerAfterCompletion();
                    }
                }, 3000); // 等待3秒后处理下一个任务
            });

        // 清理全局变量
        window.allUserData = [];
        window.currentCommentIndex = 1;
        window.totalComments = 2;
        window.dataUploadCounter = 0;
        window.multiVideoState = {
            currentVideoIndex: 0,
            totalVideos: 1,
            videosProcessed: 0,
            isMultiVideoMode: false,
            allVideosData: []
        };
    }

    // 返回搜索结果页面
    function returnToSearchResults() {
        console.log('🔙 准备返回搜索结果页面...');

        // 尝试多种返回方式
        function tryReturn() {
            // 方式1：尝试点击返回按钮
            const returnButton1 = $('#douyin-right-container > div:nth-child(6) > div > div:nth-child(1) > div.isDark.AgLgfduZ.fmVktyof > div > span');
            const returnButton2 = $('#douyin-right-container > div:nth-child(6) > div > div:nth-child(1) > div.pGZF8lyn.isDark > div');

            if (returnButton1.length > 0) {
                console.log('🔙 点击第一个返回按钮');
                returnButton1.click();

                setTimeout(() => {
                    if (returnButton2.length > 0) {
                        console.log('🔙 点击第二个返回按钮');
                        returnButton2.click();
                    }

                    // 等待页面加载后处理下一个视频
                    setTimeout(() => {
                        processNextVideo();
                    }, 3000);
                }, 1000);
            } else {
                // 方式2：使用ESC键返回
                console.log('🔙 返回按钮未找到，尝试使用ESC键返回');

                // 创建ESC键事件
                const escEvent = new KeyboardEvent('keydown', {
                    key: 'Escape',
                    code: 'Escape',
                    keyCode: 27,
                    which: 27,
                    bubbles: true,
                    cancelable: true
                });

                // 连续按两次ESC键
                document.dispatchEvent(escEvent);
                setTimeout(() => {
                    document.dispatchEvent(escEvent);

                    // 等待页面加载后处理下一个视频
                    setTimeout(() => {
                        processNextVideo();
                    }, 3000);
                }, 500);
            }
        }

        tryReturn();
    }

    // 监听页面焦点变化，检测用户详情页面关闭
    let userDetailPageOpened = false;

    // 当点击头像时标记用户详情页面已打开
    function markUserDetailPageOpened() {
        userDetailPageOpened = true;
        //console.log('🔖 标记用户详情页面已打开');
    }

    // 监听窗口焦点变化
    window.addEventListener('focus', function () {
        if (userDetailPageOpened) {
            //console.log('🔄 检测到页面重新获得焦点，可能是用户详情页面已关闭');

            // 等待一下确保页面完全加载
            setTimeout(() => {
                // 检查是否回到了视频/评论页面
                if (isVideoPage()) {
                    console.log('✅ 确认回到视频/评论页面，开始获取用户评论');

                    // 设置一个超时保护，防止getUserComment卡住
                    const commentTimeoutId = setTimeout(() => {
                        console.log('⏰ 用户评论获取超时，强制继续下一条评论');

                        // 获取当前评论索引
                        let currentIndex = 1;
                        try {
                            const storedIndex = localStorage.getItem('currentCommentIndex');
                            if (storedIndex) {
                                currentIndex = parseInt(storedIndex);
                            }
                        } catch (error) {
                            console.log('获取评论索引失败:', error);
                        }

                        // 强制处理下一条评论
                        processNextComment(currentIndex);
                    }, 15000); // 15秒超时保护

                    // 保存超时ID
                    window.currentCommentProcessTimeout = commentTimeoutId;

                    getUserComment();
                    userDetailPageOpened = false; // 重置标记
                }
            }, 1000); // 恢复到1秒
        }
    });

    // 关闭插件弹窗
    function closePluginPopup() {
        //console.log('准备关闭插件弹窗...');
        try {
            chrome.runtime.sendMessage({
                action: 'closePopup',
                success: true,
                message: '搜索完成，关闭插件弹窗'
            });
        } catch (error) {
            //console.log('发送关闭弹窗消息失败:', error);
        }
    }
    // 点击指定索引的视频
    function clickVideo(videoIndex = 0) {
        console.log(`🎯 开始查找并点击第${videoIndex + 1}个视频...`);
        updateMultiVideoStatus(`正在点击第${videoIndex + 1}个视频`);
        try {
            // 等待搜索结果完全加载，增加延迟
            setTimeout(() => {
                console.log(`🔍 搜索结果加载等待完成，开始查找第${videoIndex + 1}个视频`);

                // 优先使用您指定的选择器：.AMqhOzPC .videoImage img
                let targetVideo = null;
                let usedSelector = '';

                // 首先尝试使用您指定的选择器
                const videoImages = document.querySelectorAll('.AMqhOzPC .videoImage img');
                console.log(`📊 使用选择器 ".AMqhOzPC .videoImage img" 找到 ${videoImages.length} 个视频图片`);

                if (videoImages.length > videoIndex) {
                    targetVideo = videoImages[videoIndex];
                    usedSelector = '.AMqhOzPC .videoImage img';
                    console.log(`✅ 使用指定选择器找到第${videoIndex + 1}个视频图片`);
                } else {
                    console.log('🔄 指定选择器未找到足够的视频，尝试备用选择器');

                    // 备用选择器列表
                    const fallbackSelectors = [
                        '.AMqhOzPC .videoImage',
                        '.AMqhOzPC .LFQCShEn',
                        '.AMqhOzPC',
                        `[id^="waterfall_item_"] .VCzQd6LR`,
                        `[id^="waterfall_item_"] .rounded-2.overflow-hidden`
                    ];

                    for (let selector of fallbackSelectors) {
                        const videos = document.querySelectorAll(selector);
                        console.log(`📊 备用选择器 "${selector}" 找到 ${videos.length} 个元素`);

                        if (videos.length > videoIndex) {
                            targetVideo = videos[videoIndex];
                            usedSelector = selector;
                            console.log(`✅ 使用备用选择器 "${selector}" 找到第${videoIndex + 1}个视频`);
                            break;
                        }
                    }
                }
                if (targetVideo) {
                    console.log(`✅ 找到第${videoIndex + 1}个视频，准备点击`);
                    console.log(`📊 使用的选择器: ${usedSelector}`);
                    console.log(`📊 视频信息:`, {
                        tagName: targetVideo.tagName,
                        className: targetVideo.className,
                        src: targetVideo.src || '无src属性',
                        visible: targetVideo.offsetParent !== null
                    });

                    // 滚动到视频位置
                    targetVideo.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    console.log(`📜 已滚动到第${videoIndex + 1}个视频位置`);

                    // 增加滚动后的等待时间
                    setTimeout(() => {
                        console.log(`🖱️ 开始点击第${videoIndex + 1}个视频...`);

                        // 创建点击事件
                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        });

                        // 执行点击操作
                        targetVideo.click();
                        targetVideo.dispatchEvent(clickEvent);

                        // 如果是img元素，也尝试点击其父元素
                        if (targetVideo.tagName.toLowerCase() === 'img' && targetVideo.parentElement) {
                            console.log('🖱️ 同时点击父元素以确保响应');
                            targetVideo.parentElement.click();
                            targetVideo.parentElement.dispatchEvent(clickEvent);
                        }

                        console.log(`✅ 已执行点击第${videoIndex + 1}个视频操作`);

                        try {
                            chrome.runtime.sendMessage({
                                action: 'videoClicked',
                                success: true,
                                message: `已点击第${videoIndex + 1}个视频，正在进入评论页面`
                            });
                        } catch (error) {
                            console.log('发送视频点击消息失败:', error);
                        }

                        // 直接进入评论页面，不需要等待验证
                        setTimeout(() => {
                            pressXKey();
                        }, 3000); // 等待3秒确保页面加载完成

                    }, 1500); // 增加滚动后等待时间到1.5秒

                } else {
                    console.log(`❌ 未找到第${videoIndex + 1}个视频元素`);

                    // 如果找不到指定索引的视频，跳过并处理下一个
                    if (window.multiVideoState && window.multiVideoState.isMultiVideoMode) {
                        console.log('🔄 跳过当前视频，尝试处理下一个视频');
                        window.multiVideoState.currentVideoIndex++;
                        processNextVideo();
                    } else {
                        throw new Error(`未找到第${videoIndex + 1}个可点击的视频元素`);
                    }
                }

            }, 2000); // 恢复到2秒

        } catch (error) {
            console.error('点击视频失败：', error);
            try {
                chrome.runtime.sendMessage({
                    action: 'videoClicked',
                    success: false,
                    message: error.message
                });
            } catch (sendError) {
                //console.log('发送错误消息失败:', sendError);
            }
        }
    }

    // 兼容性函数：保持原有的clickFirstVideo函数名
    function clickFirstVideo() {
        clickVideo(0);
    }



    // 处理下一个视频
    function processNextVideo() {
        //console.log('🔄 准备处理下一个视频...');

        if (!window.multiVideoState || !window.multiVideoState.isMultiVideoMode) {
            //console.log('❌ 非多视频模式，无法处理下一个视频');
            return;
        }

        // 更新当前视频索引
        window.multiVideoState.currentVideoIndex++;

        //console.log(`📊 准备处理第${window.multiVideoState.currentVideoIndex + 1}个视频`);
        //console.log(`📊 进度: ${window.multiVideoState.videosProcessed}/${window.multiVideoState.totalVideos}`);

        // 检查是否还有视频需要处理
        if (window.multiVideoState.currentVideoIndex >= window.multiVideoState.totalVideos) {
            //console.log('🎉 所有视频处理完成！');
            finishAllVideosCollection();
            return;
        }

        // 等待页面稳定后点击下一个视频
        setTimeout(() => {
            //console.log(`🎬 开始点击第${window.multiVideoState.currentVideoIndex + 1}个视频`);
            clickVideo(window.multiVideoState.currentVideoIndex);
        }, 2000); // 恢复到2秒
    }

    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener(function (request, _sender, sendResponse) {
        //console.log('收到消息：', request);

        if (request.action === 'search') {
            //console.log('🚀 开始执行搜索流程...');

            // 保存搜索选项到localStorage
            if (request.options) {
                try {
                    localStorage.setItem('douyinSearchOptions', JSON.stringify(request.options));
                    //console.log('💾 搜索选项已保存:', request.options);

                    // 初始化多视频处理状态
                    window.multiVideoState = {
                        currentVideoIndex: 0,
                        totalVideos: request.options.video || 1,
                        videosProcessed: 0,
                        isMultiVideoMode: (request.options.video || 1) > 1,
                        allVideosData: []
                    };

                    //console.log('🎬 多视频处理状态已初始化:', window.multiVideoState);
                } catch (error) {
                    //console.log('保存搜索选项失败:', error);
                }
            }

            const result = performSearch(request.keyword);
            sendResponse(result);

            // 搜索完成后，关闭插件弹窗并继续点击视频
            if (result.success) {
                //console.log('✅ 搜索成功，开始后续流程');
                closePluginPopup();

                // 延迟后点击视频，确保弹窗关闭完成
                setTimeout(() => {
                    //console.log('🎬 开始点击第一个视频');
                    clickFirstVideo();
                }, 1000);
            } else {
                //console.log('❌ 搜索失败，停止流程');
            }

            // 延迟发送搜索结果状态
            setTimeout(() => {
                try {
                    chrome.runtime.sendMessage({
                        action: 'searchResult',
                        success: result.success,
                        message: result.message,
                        count: 0, // 这里可以根据实际情况统计结果数量
                        options: request.options // 传递选项参数
                    });
                } catch (error) {
                    //console.log('发送搜索结果消息失败:', error);
                }
            }, 1000);
        }

        return true; // 保持消息通道开放
    });

    // 批量发送所有用户数据到API
    function sendAllDataToAPI(allUsersData) {
        //console.log('📤 准备批量发送用户数据，总数:', allUsersData.length);

        if (window.apiService && window.apiService.processBatchUserData) {
            // 使用批量处理功能
            window.apiService.processBatchUserData(allUsersData);
        } else {
            //console.log('❌ API服务不可用或不支持批量处理');
        }
    }

    // 提供给API服务调用的脚本启动函数
    window.startDouyinScript = function (config) {
        //console.log('🚀 收到API任务，开始执行脚本:', config);

        // 检查是否有未上传的本地数据
        const stats = LocalDataManager.getStorageStats();
        if (stats.totalCount > 0) {
            console.log(`⚠️ 发现 ${stats.totalCount} 条未上传的本地数据，先尝试上传...`);

            DataUploadManager.uploadAllStoredData()
                .then((result) => {
                    console.log('📊 历史数据上传结果:', result);

                    if (result.success) {
                        console.log('✅ 历史数据上传成功，清理本地存储');
                        LocalDataManager.clearAllData();
                    } else {
                        console.log(`⚠️ 部分历史数据上传失败，${result.failed} 条数据保留在本地存储`);
                    }

                    // 继续执行新任务
                    startNewTask(config);
                })
                .catch((error) => {
                    console.error('❌ 历史数据上传过程出错:', error);
                    // 即使历史数据上传失败，也继续执行新任务
                    startNewTask(config);
                });
        } else {
            // 没有历史数据，直接执行新任务
            startNewTask(config);
        }
    };

    // 开始新任务
    function startNewTask(config) {
        // 只清理任务相关缓存,不清理用户数据缓存
        // 这样可以保留中途失败的数据供下次上传
        console.log('🧹 开始新任务前清理任务相关缓存...');
        clearTaskCache();

        // 确保apiTaskInfo对象存在
        if (!window.apiTaskInfo) {
            window.apiTaskInfo = {};
        }

        // 从API响应中获取任务ID,这是最关键的部分
        // 正确设置任务信息,保留从API接收的taskId
        console.log('📋 接收到的任务配置:', config);

        // 设置任务ID - 这里taskId应该是从API响应传入的
        if (config.taskId) {
            window.apiTaskInfo.taskId = config.taskId; // 使用API返回的ID
            console.log('✅ 任务ID已全局保存:', config.taskId);
            console.log('📋 全局任务信息更新:', window.apiTaskInfo);

            // 同时保存到localStorage作为备份
            try {
                localStorage.setItem('currentTaskId', config.taskId);
                console.log('💾 任务ID已备份到localStorage');
            } catch (error) {
                console.warn('⚠️ 保存任务ID到localStorage失败:', error);
            }
        } else {
            console.warn('⚠️ 未接收到任务ID,这可能导致任务完成标记和数据上传失败');
        }

        // 设置其他任务信息
        window.apiTaskInfo.receiverName = config.receiverName || 'abcdefg';
        window.apiTaskInfo.queryValue = config.searchKeyword || '';
        window.apiTaskInfo.videoNumber = config.videoCount || 1;
        window.apiTaskInfo.grabNumber = config.commentCount || 1;
        window.apiTaskInfo.isTaskActive = true;
        window.apiTaskInfo.isScriptRunning = true;

        console.log('📋 任务信息已设置:', window.apiTaskInfo);

        // 保存配置到localStorage
        const searchOptions = {
            video: config.videoCount,
            comment: config.commentCount,  // 确保使用comment字段
            includeSubComments: false
        };

        try {
            localStorage.setItem('douyinSearchOptions', JSON.stringify(searchOptions));
            localStorage.setItem('lastSearchKeyword', config.searchKeyword);
            console.log('💾 API任务配置已保存:', searchOptions);
        } catch (error) {
            //console.log('保存API任务配置失败:', error);
        }

        // 设置评论数量
        window.totalComments = config.commentCount;
        console.log('📊 设置评论数量:', window.totalComments);

        // 初始化多视频处理状态
        window.multiVideoState = {
            currentVideoIndex: 0,
            totalVideos: config.videoCount,
            videosProcessed: 0,
            isMultiVideoMode: config.videoCount > 1,
            allVideosData: []
        };

        console.log('🎬 多视频处理状态已初始化:', window.multiVideoState);

        // 开始执行搜索流程
        performSearch(config.searchKeyword);
    }

    // 本地数据存储管理器
    const LocalDataManager = {
        // 存储键前缀
        STORAGE_PREFIX: 'douyin_user_data_',

        // 保存用户数据到本地存储
        saveUserData: function (userData) {
            try {
                // 确保userData不为空
                if (!userData) {
                    console.error('❌ 用户数据为空，无法保存');
                    return null;
                }

                // 打印原始数据，便于调试
                console.log('💾 正在保存的原始用户数据:', userData);

                // 如果userData中没有必要的字段，添加默认值
                const enhancedUserData = {
                    // 确保基本字段存在
                    username: userData.username || userData.nickname || '未获取到用户名',
                    nickname: userData.nickname || userData.username || '未获取到用户名',
                    douyinId: userData.douyinId || '未获取到抖音ID',
                    avatarUrl: userData.avatarUrl || '',
                    userComment: userData.userComment || '未获取到评论',
                    commentIndex: userData.commentIndex || 1,
                    commentTimestamp: userData.commentTimestamp || new Date().toLocaleString(),
                    searchKeyword: userData.searchKeyword || '',
                    age: userData.age || '', // 【新增】确保年龄字段存在
                    userBackground: userData.userBackground || '', // 【新增】确保背景图div的style字段存在
                    // 其他可能存在的字段
                    ...userData
                };

                // 生成唯一标识符：时间戳 + 用户ID + 评论索引
                const timestamp = Date.now();
                const userId = enhancedUserData.douyinId ? enhancedUserData.douyinId.replace(/[^a-zA-Z0-9]/g, '') : 'unknown';
                const commentIndex = enhancedUserData.commentIndex || 0;
                const uniqueKey = `${this.STORAGE_PREFIX}${timestamp}_${userId}_${commentIndex}`;

                // 添加存储时间戳
                const dataWithTimestamp = {
                    ...enhancedUserData,
                    savedAt: timestamp,
                    storageKey: uniqueKey
                };

                // 格式化并保存数据
                const dataString = JSON.stringify(dataWithTimestamp);
                localStorage.setItem(uniqueKey, dataString);
                console.log(`💾 增强后的用户数据已保存到本地存储: ${uniqueKey}`);
                console.log(`💾 数据大小: ${dataString.length} 字符`);

                return uniqueKey;
            } catch (error) {
                console.error('❌ 保存用户数据到本地存储失败:', error);
                return null;
            }
        },

        // 获取所有本地存储的用户数据
        getAllStoredData: function () {
            const allData = [];
            try {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(this.STORAGE_PREFIX)) {
                        const data = localStorage.getItem(key);
                        if (data) {
                            allData.push(JSON.parse(data));
                        }
                    }
                }
                console.log(`📊 从本地存储读取到 ${allData.length} 条用户数据`);
                return allData;
            } catch (error) {
                console.error('❌ 读取本地存储数据失败:', error);
                return [];
            }
        },

        // 删除指定的用户数据
        removeUserData: function (storageKey) {
            try {
                localStorage.removeItem(storageKey);
                console.log(`🗑️ 已删除本地存储数据: ${storageKey}`);
                return true;
            } catch (error) {
                console.error('❌ 删除本地存储数据失败:', error);
                return false;
            }
        },

        // 清空所有用户数据
        clearAllData: function () {
            try {
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(this.STORAGE_PREFIX)) {
                        keysToRemove.push(key);
                    }
                }

                keysToRemove.forEach(key => localStorage.removeItem(key));
                console.log(`🧹 已清空 ${keysToRemove.length} 条本地存储数据`);
                return true;
            } catch (error) {
                console.error('❌ 清空本地存储数据失败:', error);
                return false;
            }
        },

        // 获取存储数据统计
        getStorageStats: function () {
            const allData = this.getAllStoredData();
            return {
                totalCount: allData.length,
                oldestData: allData.length > 0 ? Math.min(...allData.map(d => d.savedAt)) : null,
                newestData: allData.length > 0 ? Math.max(...allData.map(d => d.savedAt)) : null
            };
        }
    };

    // 数据上传管理器
    const DataUploadManager = {
        // 上传所有本地存储的数据
        uploadAllStoredData: async function () {
            console.log('🚀 开始上传所有本地存储的数据...');

            const allData = LocalDataManager.getAllStoredData();
            if (allData.length === 0) {
                console.log('📭 没有需要上传的数据');
                return { success: true, uploaded: 0, failed: 0, succeeded: 0 };
            }

            let uploadedCount = 0;
            let failedCount = 0;
            // 记录成功上传的数据的storageKey
            this.successfullyUploadedKeys = [];

            for (let i = 0; i < allData.length; i++) {
                const userData = allData[i];
                console.log(`📤 正在上传第 ${i + 1}/${allData.length} 条数据...`);

                try {
                    const success = await this.uploadSingleUserData(userData);
                    if (success) {
                        // 记录成功上传的数据的storageKey
                        this.successfullyUploadedKeys.push(userData.storageKey);
                        uploadedCount++;
                        console.log(`✅ 第 ${i + 1} 条数据上传成功`);
                    } else {
                        failedCount++;
                        console.log(`❌ 第 ${i + 1} 条数据上传失败，保留在本地存储`);
                    }
                } catch (error) {
                    failedCount++;
                    console.error(`❌ 第 ${i + 1} 条数据上传异常:`, error);
                }

                // 添加延迟避免请求过于频繁
                if (i < allData.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            console.log(`📊 数据上传完成: 成功 ${uploadedCount} 条, 失败 ${failedCount} 条`);
            return {
                success: failedCount === 0,
                uploaded: uploadedCount,
                failed: failedCount,
                succeeded: uploadedCount // 添加成功上传的数量
            };
        },

        // 清理已成功上传的数据
        clearSuccessfullyUploadedData: function () {
            if (!this.successfullyUploadedKeys || this.successfullyUploadedKeys.length === 0) {
                console.log('📭 没有需要清理的已上传数据');
                return 0;
            }

            let clearedCount = 0;
            for (const key of this.successfullyUploadedKeys) {
                if (LocalDataManager.removeUserData(key)) {
                    clearedCount++;
                }
            }

            console.log(`🧹 已清理 ${clearedCount}/${this.successfullyUploadedKeys.length} 条成功上传的数据`);
            this.successfullyUploadedKeys = []; // 清空记录
            return clearedCount;
        },

        // 上传单条用户数据
        uploadSingleUserData: function (userData) {
            return new Promise((resolve) => {
                if (!window.apiService || !window.apiService.sendUserData) {
                    console.error('❌ API服务不可用');
                    resolve(false);
                    return;
                }

                // 获取taskId，优先从全局变量，备用从localStorage
                let taskId = '';
                if (window.apiTaskInfo && window.apiTaskInfo.taskId) {
                    taskId = window.apiTaskInfo.taskId;
                    console.log('📋 使用全局保存的taskId:', taskId);
                } else {
                    // 尝试从localStorage获取备用taskId
                    try {
                        taskId = localStorage.getItem('currentTaskId') || '';
                        if (taskId) {
                            console.log('📋 使用localStorage备用taskId:', taskId);
                        } else {
                            console.warn('⚠️ 未找到taskId，数据上传可能失败');
                        }
                    } catch (error) {
                        console.error('❌ 获取备用taskId失败:', error);
                    }
                }

                // 构建API数据格式，确保字段映射正确
                const apiData = {
                    task_id: taskId,
                    query_value: userData.searchKeyword || '',
                    receiver_name: window.apiTaskInfo ? window.apiTaskInfo.receiverName || '' : '',
                    dy_comment_level: userData.commentIndex ? userData.commentIndex.toString() : '',
                    dy_comment: userData.userComment || '',
                    dy_nick_name: userData.username || userData.nickname || '',
                    dy_user_id: userData.douyinId || '',
                    dy_remark: userData.userBio || userData.signature || '',
                    dy_ip: userData.userLocation || userData.ipLocation || '',
                    dy_school: userData.university || userData.locationTag || '',
                    dy_attention_number: userData.followingCount || userData.following || '',
                    dy_follower_number: userData.followersCount || userData.fans || '',
                    dy_production_number: userData.likesCount || userData.likes || '',
                    dy_head_img: userData.avatarUrl || '',
                    dy_compilations_name: userData.compilationsName || '', // 添加编辑内容字段
                    dy_age: userData.age || '', // 【新增】用户年龄
                    dy_user_background: userData.userBackground || '', // 【新增】背景图div的style
                    dy_like_tag: userData.isLikedByAuthor !== undefined ? userData.isLikedByAuthor.toString() : '0' // 【新增】作者赞过标记
                };

                // 检查关键字段
                if (!apiData.dy_comment && userData.userComment) {
                    apiData.dy_comment = userData.userComment;
                }

                if (!apiData.dy_nick_name && (userData.nickname || userData.username)) {
                    apiData.dy_nick_name = userData.nickname || userData.username;
                }

                if (!apiData.dy_user_id && userData.douyinId) {
                    apiData.dy_user_id = userData.douyinId;
                }

                // 确保评论层级为字符串
                if (apiData.dy_comment_level && typeof apiData.dy_comment_level !== 'string') {
                    apiData.dy_comment_level = apiData.dy_comment_level.toString();
                }

                console.log('📤 发送API数据:', apiData);

                window.apiService.sendUserData(apiData)
                    .then(() => {
                        console.log('✅ API数据发送成功');
                        resolve(true);
                    })
                    .catch((error) => {
                        console.error('❌ API发送失败:', error);
                        resolve(false);
                    });
            });
        }
    };



    console.log('抖音群聊搜索插件初始化完成');



})();









